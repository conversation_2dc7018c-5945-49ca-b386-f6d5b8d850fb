import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  resolve: {
    // Preserve symlinks to maintain the connection to the shared packages
    preserveSymlinks: true,
    alias: {
      // Ensure juice is resolved from the main node_modules
      'juice': resolve(__dirname, 'node_modules/juice/index.js')
    }
  },
  optimizeDeps: {
    // Include juice in dependency optimization
    include: ['juice']
  },
  define: {
    // Ensure global is defined for Node.js modules
    global: 'globalThis'
  }
});
