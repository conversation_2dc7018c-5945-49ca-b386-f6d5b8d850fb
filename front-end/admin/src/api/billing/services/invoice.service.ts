import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { Account } from '@api/profile/models/account.interface';
import { IndexRequest } from '@api/support/requests/index.request';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@helpers/transform-params';
import { Invoice } from '@api/billing/models/invoice.interface';

@Injectable({
  providedIn: 'root',
})
export class InvoiceService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    account: Account,
    request: IndexRequest,
  ): Observable<DataResponse<Invoice[]> | PaginatedResponse<Invoice>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      DataResponse<Invoice[]> | PaginatedResponse<Invoice>
    >(`${this.endpoint}/api/v1/admin/accounts/${account.id}/invoices`, {
      params,
    });
  }
}
