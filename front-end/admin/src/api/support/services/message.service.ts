import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from '@services/config.service';
import { Ticket } from '@api/support/models/ticket.interface';
import { IndexRequest } from '@api/support/requests/index.request';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Message } from '@api/support/models/message.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@helpers/transform-params';
import { MessageStoreRequest } from '@api/support/requests/message-store.request';
import { bodyToFormData } from '@helpers/body-to-form-data';
import { DateCollection } from '@api/support/responses/date-collection.interface';

@Injectable({
  providedIn: 'root',
})
export class MessageService {
  private readonly endpoint: string | undefined;

  constructor(
    private httpClient: HttpClient,
    private configService: ConfigService,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(ticket: Ticket): Observable<DataResponse<DateCollection>> {
    return this.httpClient.get<DataResponse<DateCollection>>(
      `${this.endpoint}/api/v1/admin/support/tickets/${ticket.id}/messages`,
    );
  }

  public store(
    ticket: Ticket,
    body: MessageStoreRequest,
  ): Observable<DataResponse<Message>> {
    return this.httpClient.post<DataResponse<Message>>(
      `${this.endpoint}/api/v1/admin/support/tickets/${ticket.id}/messages`,
      bodyToFormData(body),
    );
  }
}
