import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { IndexRequest } from '@api/support/requests/index.request';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Ticket } from '@api/support/models/ticket.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@helpers/transform-params';
import { TicketStatusRequest } from '@api/support/requests/ticket-status.request';
import { TicketIndexRequest } from '@api/support/requests/ticket-index.request';

@Injectable({
  providedIn: 'root',
})
export class TicketService {
  private readonly endpoint: string | undefined;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: TicketIndexRequest,
  ): Observable<DataResponse<Ticket[]> | PaginatedResponse<Ticket>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      DataResponse<Ticket[]> | PaginatedResponse<Ticket>
    >(`${this.endpoint}/api/v1/admin/support/tickets`, { params });
  }

  public show(id: string): Observable<DataResponse<Ticket>> {
    return this.httpClient.get<DataResponse<Ticket>>(
      `${this.endpoint}/api/v1/admin/support/tickets/${id}`,
    );
  }

  public status(
    ticket: Ticket,
    body: TicketStatusRequest,
  ): Observable<DataResponse<Ticket>> {
    return this.httpClient.put<DataResponse<Ticket>>(
      `${this.endpoint}/api/v1/admin/support/tickets/${ticket.id}/status`,
      body,
    );
  }
}
