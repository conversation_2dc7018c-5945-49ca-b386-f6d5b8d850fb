import { TicketStatus } from '@api/support/enums/ticket-status.enum';
import { User } from '@api/profile/models/user.interface';
import { Attachment } from '@api/support/models/attachment.interface';
import { Message } from '@api/support/models/message.interface';

export interface Ticket {
  id: string;
  account_id: string;
  status: TicketStatus;
  title: string;
  description: string;
  created_at: string;
  updated_at: string;
  created_by?: User;
  attachments?: Attachment[];
  messages?: Message[];
}
