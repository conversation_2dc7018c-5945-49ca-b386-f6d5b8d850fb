import { StripeInformation } from '@api/billing/models/stripe-information.interface';
import { Subscription } from '@api/billing/models/subscription.interface';

export interface Account {
  id: string;
  stripe_id: string | null;
  name: string;
  verified_at: string | null;
  features: { [key: string]: boolean };
  last_login_at: string | null;
  stripe_information?: StripeInformation;
  active_subscription?: Subscription;
}
