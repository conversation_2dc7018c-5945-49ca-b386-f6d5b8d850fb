import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { IndexRequest } from '@api/support/requests/index.request';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Account } from '@api/profile/models/account.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@helpers/transform-params';
import { AccountInviteRequest } from '@api/profile/requests/account-invite.request';

@Injectable({
  providedIn: 'root',
})
export class AccountService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: IndexRequest,
  ): Observable<DataResponse<Account[]> | PaginatedResponse<Account>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      DataResponse<Account[]> | PaginatedResponse<Account>
    >(`${this.endpoint}/api/v1/admin/accounts`, { params });
  }

  public show(id: number): Observable<DataResponse<Account>> {
    return this.httpClient.get<DataResponse<Account>>(
      `${this.endpoint}/api/v1/admin/accounts/${id}`,
    );
  }

  public toggleVerification(
    account: Account,
  ): Observable<DataResponse<Account>> {
    return this.httpClient.post<DataResponse<Account>>(
      `${this.endpoint}/api/v1/admin/accounts/${account.id}/verification/toggle`,
      {},
    );
  }

  public delete(account: Account): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/v1/admin/accounts/${account.id}`,
    );
  }

  public invite(body: AccountInviteRequest): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/v1/admin/accounts/invite`,
      body,
    );
  }
}
