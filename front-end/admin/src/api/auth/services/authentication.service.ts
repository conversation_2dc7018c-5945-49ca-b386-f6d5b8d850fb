import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ConfigService } from '@app/services/config.service';
import { LoginRequest } from '../requests/login.request';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Admin } from '@api/auth/models/admin.model';

@Injectable({
  providedIn: 'root',
})
export class AuthenticationService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public login(body: LoginRequest): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/v1/admin/auth/login`,
      body,
    );
  }

  public logout(): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/v1/admin/auth/logout`,
      {},
    );
  }

  public me(): Observable<DataResponse<Admin>> {
    return this.httpClient.get<DataResponse<Admin>>(
      `${this.endpoint}/api/v1/admin/auth/me`,
    );
  }

  public token(): Observable<void> {
    return this.httpClient.get<void>(`${this.endpoint}/sanctum/csrf-cookie`);
  }
}
