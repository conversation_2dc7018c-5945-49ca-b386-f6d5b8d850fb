import { Editor, Components } from 'grapesjs';
import { PluginOptions } from '.';

export default (editor: Editor, opts: Required<PluginOptions>) => {
  editor.Commands.add(opts.cmdTglImages, {
    run(editor: Editor) {
      const components = editor.getComponents();
      this.toggleImages(components);
    },

    stop(editor: Editor) {
      const components = editor.getComponents();
      this.toggleImages(components, true);
    },

    toggleImages(components: Components, on: boolean = false) {
      const srcPlh = '##';

      components.forEach((component: any) => {
        if (component.get('type') === 'image') {
          const source = component.get('src');

          if (on) {
            if (source === srcPlh) {
              component.set('src', component.get('src_bkp'));
            }
          } else if (source !== srcPlh) {
            component.set('src_bkp', component.get('src'));
            component.set('src', srcPlh);
          }
        }

        this.toggleImages(component.components(), on);
      });
    },
  });
};
