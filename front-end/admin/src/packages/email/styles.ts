import { Editor } from 'grapesjs';
import { PluginOptions } from '.';

export default function (editor: Editor, opts: Required<PluginOptions>) {
  let sectors = editor.StyleManager.getSectors();

  if (opts.updateStyleManager) {
    const styleManagerSectors = [
      {
        name: 'Dimension',
        open: false,
        buildProps: [
          'width',
          'height',
          'max-width',
          'min-height',
          'margin',
          'padding',
        ],
        properties: [
          {
            property: 'margin',
            properties: [
              { name: 'Top', property: 'margin-top' },
              { name: 'Left', property: 'margin-left' },
              { name: 'Right', property: 'margin-right' },
              { name: 'Bottom', property: 'margin-bottom' },
            ],
          },
          {
            property: 'padding',
            properties: [
              { name: 'Top', property: 'padding-top' },
              { name: 'Right', property: 'padding-right' },
              { name: 'Bottom', property: 'padding-bottom' },
              { name: 'Left', property: 'padding-left' },
            ],
          },
        ],
      },
      {
        name: 'Typography',
        open: false,
        buildProps: [
          'font-family',
          'font-size',
          'font-weight',
          'letter-spacing',
          'color',
          'line-height',
          'text-align',
          'text-decoration',
          'font-style',
          'vertical-align',
          'text-shadow',
        ],
        properties: [
          { name: 'Font', property: 'font-family' },
          { name: 'Weight', property: 'font-weight' },
          { name: 'Font color', property: 'color' },
          {
            property: 'text-align',
            type: 'radio',
            defaults: 'left',
            list: [
              { value: 'left', name: 'Left', className: 'fa fa-align-left' },
              {
                value: 'center',
                name: 'Center',
                className: 'fa fa-align-center',
              },
              { value: 'right', name: 'Right', className: 'fa fa-align-right' },
              {
                value: 'justify',
                name: 'Justify',
                className: 'fa fa-align-justify',
              },
            ],
          },
          {
            property: 'text-decoration',
            type: 'radio',
            defaults: 'none',
            list: [
              { value: 'none', name: 'None', className: 'fa fa-times' },
              {
                value: 'underline',
                name: 'underline',
                className: 'fa fa-underline',
              },
              {
                value: 'line-through',
                name: 'Line-through',
                className: 'fa fa-strikethrough',
              },
            ],
          },
          {
            property: 'font-style',
            type: 'radio',
            defaults: 'normal',
            list: [
              { value: 'normal', name: 'Normal', className: 'fa fa-font' },
              { value: 'italic', name: 'Italic', className: 'fa fa-italic' },
            ],
          },
          {
            property: 'vertical-align',
            type: 'select',
            defaults: 'baseline',
            list: [
              { value: 'baseline' },
              { value: 'top' },
              { value: 'middle' },
              { value: 'bottom' },
            ],
          },
          {
            property: 'text-shadow',
            properties: [
              { name: 'X position', property: 'text-shadow-h' },
              { name: 'Y position', property: 'text-shadow-v' },
              { name: 'Blur', property: 'text-shadow-blur' },
              { name: 'Color', property: 'text-shadow-color' },
            ],
          },
        ],
      },
      {
        name: 'Decorations',
        open: false,
        buildProps: [
          'background-color',
          'border-collapse',
          'border-radius',
          'border',
          'background',
          'box-shadow',
        ],
        properties: [
          {
            property: 'background-color',
            name: 'Background',
          },
          {
            property: 'border-radius',
            properties: [
              { name: 'Top', property: 'border-top-left-radius' },
              { name: 'Right', property: 'border-top-right-radius' },
              { name: 'Bottom', property: 'border-bottom-left-radius' },
              { name: 'Left', property: 'border-bottom-right-radius' },
            ],
          },
          {
            property: 'border-collapse',
            type: 'radio',
            defaults: 'separate',
            list: [
              { value: 'separate', name: 'No' },
              { value: 'collapse', name: 'Yes' },
            ],
          },
          {
            property: 'border',
            properties: [
              { name: 'Width', property: 'border-width', defaults: '0' },
              { name: 'Style', property: 'border-style' },
              { name: 'Color', property: 'border-color' },
            ],
          },
          {
            property: 'background',
            properties: [
              { name: 'Image', property: 'background-image' },
              { name: 'Repeat', property: 'background-repeat' },
              { name: 'Position', property: 'background-position' },
              { name: 'Attachment', property: 'background-attachment' },
              { name: 'Size', property: 'background-size' },
            ],
          },
          {
            name: 'box-shadow',
            open: false,
            buildProps: [
              'box-shadow-h',
              'box-shadow-v',
              'box-shadow-blur',
              'box-shadow-spread',
              'box-shadow-color',
              'box-shadow-type',
            ],
            properties: [
              {
                id: 'box-shadow-h',
                name: 'Shadow Horizontal',
                property: 'box-shadow-h',
                type: 'integer',
                units: ['px', 'em', '%'],
                defaults: 0,
              },
              {
                id: 'box-shadow-v',
                name: 'Shadow Vertical',
                property: 'box-shadow-v',
                type: 'integer',
                units: ['px', 'em', '%'],
                defaults: 0,
              },
              {
                id: 'box-shadow-blur',
                name: 'Shadow Blur',
                property: 'box-shadow-blur',
                type: 'integer',
                units: ['px', 'em', '%'],
                defaults: 5,
                min: 0,
              },
              {
                id: 'box-shadow-spread',
                name: 'Shadow Spread',
                property: 'box-shadow-spread',
                type: 'integer',
                units: ['px', 'em', '%'],
                defaults: 0,
              },
              {
                id: 'box-shadow-color',
                name: 'Shadow Color',
                property: 'box-shadow-color',
                type: 'color',
                defaults: 'rgba(0,0,0,0.5)',
              },
              {
                id: 'box-shadow-type',
                name: 'Shadow Type',
                property: 'box-shadow-type',
                type: 'select',
                defaults: '',
                list: [
                  { value: '', name: 'Outside' },
                  { value: 'inset', name: 'Inside' },
                ],
              },
            ],
          },
        ],
      },
    ];

    editor.onReady(() => {
      sectors.reset();
      sectors.add(styleManagerSectors);

      editor.on('style:property:update', function (eventData: any) {
        // Extract the property information from the event data
        const propertyObj = eventData.property;
        const value = eventData.value;

        // Skip if not a string value or already has !important
        if (
          !value ||
          typeof value !== 'string' ||
          value.includes('!important')
        ) {
          return;
        }

        // Get the property name from the property object
        const propName = propertyObj.attributes.property;

        if (
          propName.includes('margin') ||
          propName.includes('padding') ||
          (propName.includes('border') && propName.includes('radius'))
        ) {
          return;
        }

        // Add !important to the value
        const importantValue = value + ' !important';

        // Get the selected component
        const selected = editor.getSelected();
        if (!selected) return;

        // Create a style object with our !important value
        const style = {};
        // @ts-ignore
        style[propName] = importantValue;

        // Apply the style to the component
        setTimeout(function () {
          // Get current styles to preserve other properties
          const currentStyles = selected.getStyle();

          if (propName === 'color') {
            if (eventData.from.value) {
              currentStyles[propName] = importantValue;
            }
          } else {
            // Update with our !important style
            currentStyles[propName] = importantValue;
          }

          // Apply the updated styles
          selected.setStyle(currentStyles);
        }, 0);
      });
    });
  }
}
