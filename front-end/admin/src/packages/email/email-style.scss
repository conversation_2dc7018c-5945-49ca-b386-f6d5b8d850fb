#blocks {
  @apply w-full p-2 h-full;
  .gjs-blocks-c {
    @apply block space-y-4 bg-gray-100;
    .gjs-block {
      @apply min-w-fit w-full m-0 flex-row justify-normal bg-gray-100 min-h-0 items-center space-x-2 rounded-lg p-2 hover:shadow-none shadow-none hover:bg-eaglo-blue/10 border-gray-300;

      .gjs-block__media {
        @apply w-10 h-10 m-0 text-eaglo-blue
      }

      .gjs-block-label {
        @apply hover:text-eaglo-gray-700 text-eaglo-gray-700 text-sm
      }
    }
  }
}

#panel-views {
  @apply relative w-full border-0 p-0 bg-gray-100;

  .gjs-pn-buttons {
    @apply grid grid-cols-4;
  }

  .gjs-pn-btn {
    @apply shadow-none text-eaglo-gray-700 px-4 py-2 m-0 text-sm bg-gray-200;
  }

  .gjs-pn-active {
    @apply bg-eaglo-blue text-white;
  }
}

#gjs {
  @apply w-full;
  .gjs-cv-canvas {
    @apply w-full top-0 border border-gray-300 rounded-lg shadow;
  }

  .gjs-editor {
    @apply bg-gray-100
  }
}

#devices {
  @apply relative p-0 left-0 bg-gray-100;

  .gjs-pn-btn {
    @apply text-eaglo-gray-700;

    &.gjs-pn-active {
      @apply text-eaglo-blue! shadow-none! bg-gray-100!;
    }
  }
}

#options {
  @apply relative p-0 right-0 bg-gray-100;

  .gjs-pn-btn {
    @apply text-eaglo-gray-700;

    &.gjs-pn-active {
      @apply text-eaglo-blue! shadow-none! bg-gray-100!;
    }
  }
}

.gjs-sm-sectors {
  @apply p-2! bg-gray-100! space-y-2!;
  .gjs-sm-sector {
    @apply bg-gray-100 text-eaglo-gray-700 border border-gray-300 rounded-lg overflow-hidden;

    .gjs-sm-sector-title {
      @apply bg-gray-100 border-0;

      .gjs-sm-sector-label {
        @apply text-eaglo-gray-700 font-normal text-base;
      }
    }

    .gjs-sm-icon {
      @apply font-normal text-sm text-eaglo-gray-700;
    }

    .gjs-field {
      @apply bg-white;
    }

    .gjs-sm-properties {
      @apply bg-gray-100 p-0 border-0;
    }

    .gjs-sm-field {
      @apply border-0;
    }

    input {
      @apply text-eaglo-gray-700;
    }
  }
}

.gjs-clm-tags {
  @apply bg-gray-100! p-2!;

  .gjs-clm-tags-field {
    @apply bg-white;
  }

  .gjs-field {
    @apply bg-white;
  }

  input {
    @apply text-eaglo-gray-700!;
  }
}

.gjs-traits-c {
  @apply bg-gray-100! p-2!;

  .gjs-field {
    @apply bg-white;
  }

  input {
    @apply text-eaglo-gray-700!;
  }

  .gjs-label {
    @apply font-normal text-base;
  }
}

.gjs-field select {
  @apply text-eaglo-gray-700!
}

.gjs-input-unit {
  @apply text-eaglo-gray-700!;
}

.gjs-radio-item:has(.gjs-sm-radio:checked) {
  /* Add your styling here */
background-color: var(--gjs-main-dark-color)
}

