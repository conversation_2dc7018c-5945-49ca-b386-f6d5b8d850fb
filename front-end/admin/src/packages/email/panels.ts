import { Editor } from 'grapesjs';
import { PluginOptions } from '.';
import {
  cmdClear,
  cmdDeviceDesktop,
  cmdDeviceMobile,
  cmdDeviceTablet,
} from './consts';

export default (editor: Editor, opts: Required<PluginOptions>) => {
  const { Panels } = editor;
  const { cmdOpenImport, cmdTglImages } = opts;
  const openExport = 'export-template';
  const openStyleManager = 'open-sm';
  const openTraits = 'open-tm';
  const openLayers = 'open-layers';
  const openBlocks = 'open-blocks';
  const activateOutline = 'sw-visibility';
  const activateFullscreen = 'fullscreen';
  const activatePreview = 'preview';
  const iconStyle = 'style="display: block; max-width: 22px"';

  // Turn off default devices select and create new one
  editor.getConfig().showDevices = false;

  Panels.getPanels().reset([
    {
      id: 'devices-c',
      el: '#devices',
      buttons: [
        {
          id: cmdDeviceDesktop,
          command: cmdDeviceDesktop,
          active: true,
          label: `<svg ${iconStyle} viewBox="0 0 24 24">
            <path fill="currentColor" d="M21,16H3V4H21M21,2H3C1.89,2 1,2.89 1,4V16A2,2 0 0,0 3,18H10V20H8V22H16V20H14V18H21A2,2 0 0,0 23,16V4C23,2.89 22.1,2 21,2Z" />
        </svg>`,
        },
        {
          id: cmdDeviceTablet,
          command: cmdDeviceTablet,
          label: `<svg ${iconStyle} viewBox="0 0 24 24">
            <path fill="currentColor" d="M19,18H5V6H19M21,4H3C1.89,4 1,4.89 1,6V18A2,2 0 0,0 3,20H21A2,2 0 0,0 23,18V6C23,4.89 22.1,4 21,4Z" />
        </svg>`,
        },
        {
          id: cmdDeviceMobile,
          command: cmdDeviceMobile,
          label: `<svg ${iconStyle} viewBox="0 0 24 24">
            <path fill="currentColor" d="M17,19H7V5H17M17,1H7C5.89,1 5,1.89 5,3V21A2,2 0 0,0 7,23H17A2,2 0 0,0 19,21V3C19,1.89 18.1,1 17,1Z" />
        </svg>`,
        },
      ],
    },
    {
      id: 'options',
      el: '#options',
      buttons: [
        {
          id: activateOutline,
          command: activateOutline,
          context: activateOutline,
          label: `<svg ${iconStyle} viewBox="0 0 24 24">
        <path fill="currentColor" d="M15,5H17V3H15M15,21H17V19H15M11,5H13V3H11M19,5H21V3H19M19,9H21V7H19M19,21H21V19H19M19,13H21V11H19M19,17H21V15H19M3,5H5V3H3M3,9H5V7H3M3,13H5V11H3M3,17H5V15H3M3,21H5V19H3M11,21H13V19H11M7,21H9V19H7M7,5H9V3H7V5Z" />
    </svg>`,
        },
        {
          id: cmdTglImages,
          command: cmdTglImages,
          label: `<svg ${iconStyle} viewBox="0 0 24 24">
          <path fill="currentColor" d="M5 3C3.9 3 3 3.9 3 5V19C3 20.11 3.9 21 5 21H14.09C14.03 20.67 14 20.34 14 20C14 19.32 14.12 18.64 14.35 18H5L8.5 13.5L11 16.5L14.5 12L16.73 14.97C17.7 14.34 18.84 14 20 14C20.34 14 20.67 14.03 21 14.09V5C21 3.89 20.1 3 19 3H5M16.47 17.88L18.59 20L16.47 22.12L17.88 23.54L20 21.41L22.12 23.54L23.54 22.12L21.41 20L23.54 17.88L22.12 16.46L20 18.59L17.88 16.47L16.46 17.88Z"/>
        </svg>`,
        },
        {
          id: 'undo',
          command: 'core:undo',
          label: `<svg ${iconStyle} viewBox="0 0 24 24">
            <path fill="currentColor" d="M20 13.5C20 17.09 17.09 20 13.5 20H6V18H13.5C16 18 18 16 18 13.5S16 9 13.5 9H7.83L10.91 12.09L9.5 13.5L4 8L9.5 2.5L10.92 3.91L7.83 7H13.5C17.09 7 20 9.91 20 13.5Z" />
        </svg>`,
        },
        {
          id: 'redo',
          command: 'core:redo',
          label: `<svg ${iconStyle} viewBox="0 0 24 24">
            <path fill="currentColor" d="M10.5 18H18V20H10.5C6.91 20 4 17.09 4 13.5S6.91 7 10.5 7H16.17L13.08 3.91L14.5 2.5L20 8L14.5 13.5L13.09 12.09L16.17 9H10.5C8 9 6 11 6 13.5S8 18 10.5 18Z" />
        </svg>`,
        },
        {
          id: cmdClear,
          command: cmdClear,
          label: `<svg ${iconStyle} viewBox="0 0 24 24">
              <path fill="currentColor" d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
          </svg>`,
        },
      ],
    },
    {
      id: 'views',
      el: '#panel-views',
      buttons: [
        {
          id: openBlocks,
          command: openBlocks,
          label: 'Elements',
          active: true,
          togglable: false,
        },
        {
          id: openStyleManager,
          command: openStyleManager,
          label: 'Styles',
        },
        {
          id: openTraits,
          command: openTraits,
          label: 'Settings',
        },
        {
          id: openLayers,
          command: openLayers,
          label: 'Layers',
        },
      ],
    },
  ]);

  // On component change show the Style Manager
  opts.showStylesOnChange &&
    editor.on('component:selected', () => {
      const openLayersBtn = Panels.getButton('views', openLayers);

      // Don't switch when the Layer Manager is on or there is no selected components
      if (
        (!openLayersBtn || !openLayersBtn.get('active')) &&
        editor.getSelected()
      ) {
        const openSmBtn = Panels.getButton('views', openStyleManager);
        openSmBtn?.set('active', true);
      }
    });

  editor.Commands.add(openBlocks, {
    getRowEl(editor: any) {
      return editor.getContainer().closest('#email-container');
    },
    getStyleEl(row: any) {
      return row.querySelector('#blocks');
    },

    run(editor: any, sender: any) {
      const smEl = this.getStyleEl(this.getRowEl(editor));
      smEl.style.display = '';
    },
    stop(editor: any, sender: any) {
      const smEl = this.getStyleEl(this.getRowEl(editor));
      smEl.style.display = 'none';
    },
  });

  editor.Commands.add(openStyleManager, {
    getRowEl(editor: any) {
      return editor.getContainer().closest('#email-container');
    },
    getStyleEl(row: any) {
      return row.querySelector('#styles-container');
    },

    run(editor: any, sender: any) {
      const smEl = this.getStyleEl(this.getRowEl(editor));
      smEl.style.display = '';
    },
    stop(editor: any, sender: any) {
      const smEl = this.getStyleEl(this.getRowEl(editor));
      smEl.style.display = 'none';
    },
  });

  editor.Commands.add(openTraits, {
    getRowEl(editor: any) {
      return editor.getContainer().closest('#email-container');
    },
    getStyleEl(row: any) {
      return row.querySelector('#traits-container');
    },

    run(editor: any, sender: any) {
      const smEl = this.getStyleEl(this.getRowEl(editor));
      smEl.style.display = '';
    },
    stop(editor: any, sender: any) {
      const smEl = this.getStyleEl(this.getRowEl(editor));
      smEl.style.display = 'none';
    },
  });

  editor.Commands.add(openLayers, {
    getRowEl(editor: any) {
      return editor.getContainer().closest('#email-container');
    },
    getStyleEl(row: any) {
      return row.querySelector('#layers-container');
    },

    run(editor: any, sender: any) {
      const smEl = this.getStyleEl(this.getRowEl(editor));
      smEl.style.display = '';
    },
    stop(editor: any, sender: any) {
      const smEl = this.getStyleEl(this.getRowEl(editor));
      smEl.style.display = 'none';
    },
  });

  // Do stuff on load
  editor.onReady(() => {
    if (opts.showBlocksOnLoad) {
      const openBlocksBtn = Panels.getButton('views', openBlocks);
      openBlocksBtn?.set('active', true);
    }
  });
};
