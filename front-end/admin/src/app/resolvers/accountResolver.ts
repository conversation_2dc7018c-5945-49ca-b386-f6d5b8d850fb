import { ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import { AuthService } from '@services/auth.service';
import { Observable, tap } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Admin } from '@api/auth/models/admin.model';
import { Account } from '@api/profile/models/account.interface';
import { AccountService } from '@api/profile/services/account.service';
import { AccountState } from '@app/states/account.state';

export const accountResolver: ResolveFn<Observable<DataResponse<Account>>> = (
  route,
  state,
) => {
  const accountService = inject(AccountService);
  const accountState = inject(AccountState);

  return accountService.show(route.params['id']).pipe(
    tap((response) => {
      accountState.account.set(response.data);
    }),
  );
};
