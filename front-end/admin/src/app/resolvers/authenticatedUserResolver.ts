import { ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import { AuthService } from '@services/auth.service';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Admin } from '@api/auth/models/admin.model';

export const authenticatedUserResolver: ResolveFn<
  Observable<DataResponse<Admin>>
> = (route, state) => {
  const authenticationService = inject(AuthService);
  return authenticationService.loadUser();
};
