<div *transloco="let t; read: 'pages.emails.detail'" class="space-y-4">
    <div class="flex justify-end items-center">
      <a routerLink="/emails" class="link">{{ 'general.buttons.cancel' | transloco}}</a>
      <button (click)="submit()" [class.loading]="saveLoading" class="btn --light-blue">{{ 'general.buttons.save' | transloco }}</button>
    </div>


  <main class="h-full space-y-8">
    @if(loading) {
      <div class="h-full w-full flex items-center justify-center">
        <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-4xl"></i>
      </div>
    }

    @if (!loading) {
      <div>
        <div class="border-b border-gray-200">
          <div>
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
              <button (click)="navigateTab(tabs.GENERAL)" [class.active]="activeTab === tabs.GENERAL">
                <i class="fa-regular fa-circle-info"></i>
                <span>{{ t('tabs.general') }}</span>
              </button>
              <button (click)="navigateTab(tabs.TEMPLATE)" [class.active]="activeTab === tabs.TEMPLATE">
                <i class="fa-regular fa-file-invoice"></i>
                <span>{{ t('tabs.template') }}</span>
              </button>
            </nav>
          </div>
        </div>
      </div>

      @if(activeTab === tabs.GENERAL && form) {
        <form [formGroup]="form" class="grid grid-cols-1 gap-x-8 gap-y-10 border-b-0 border-gray-900/10 pb-12 md:grid-cols-3">

          <div class="grid max-w-2xl grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2 md:col-span-2">
            <div class="toggle col-span-4">
              <input [formControl]="form.controls.whitelabel" id="whitelabel" type="checkbox">
              <label for="whitelabel">{{ t('general.whitelabel') }}</label>
            </div>

            <div class="form-group">
              <input [formControl]="form.controls.name" type="text">
              <label>{{ t('general.name') }}</label>
            </div>

            <div class="form-group">
              <input [formControl]="form.controls.subject" type="text">
              <label>{{ t('general.subject') }}</label>
            </div>
          </div>
        </form>
      }

      @if (activeTab === tabs.TEMPLATE) {
        <div class="space-y-4">
          <div (click)="toggleShowLegend()" class="flex space-x-4 cursor-pointer items-center">
            <h2 class="font-bold">{{ t('template.variables') }}</h2>
            <span [class.rotate-180]="showLegend" class="transition ease-in-out"><i class="fa-solid fa-chevron-down"></i></span>
          </div>

          @if(showLegend) {
<!--            <div>-->
<!--              <components-table [response]="(email?.variables  ?? {})| keyvalue">-->
<!--                <ng-template #headers>-->
<!--                    <th>{{ t('template.table.name') }}</th>-->
<!--                    <th>{{ t('template.table.how_to_use') }}</th>-->
<!--                </ng-template>-->
<!--                <ng-template #rows let-row>-->
<!--                  <td>{{ row.value.name }}</td>-->
<!--                  <td>{{ row.value.value }}</td>-->
<!--                </ng-template>-->
<!--              </components-table>-->
<!--            </div>-->
          }
        </div>
      }

      <div [class.hidden]="activeTab === tabs.GENERAL" id="email-container" class="pb-8 space-y-4">
        <div class="flex space-x-2">
          <div class="border border-gray-300 rounded-lg overflow-hidden w-1/3 h-[80vh]">
            <div id="panel-views"></div>
            <div class="h-[95%] overflow-y-auto">
              <div id="blocks"></div>
              <div id="styles-container" style="display: none"></div>
              <div id="traits-container" style="display: none"></div>
              <div id="layers-container" style="display: none"></div>
            </div>
          </div>
          <div class="w-2/3 space-y-1">
            <div class="flex justify-between">
              <div id="devices"></div>
              <div id="options"></div>
            </div>
            <div [class.hidden]="activeTab !== tabs.TEMPLATE" id="gjs" class="">
            </div>
          </div>
        </div>
      </div>
    }
  </main>
</div>
