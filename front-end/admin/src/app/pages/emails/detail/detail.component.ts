import { Component, DestroyRef, Input, NgZone, OnInit } from '@angular/core';
import { Email } from '@api/email/models/email.interface';
import { EmailService } from '@api/email/services/email.service';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router, RouterLink } from '@angular/router';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { EmailRequest } from '@api/email/requests/email.request';
import { KeyValuePipe } from '@angular/common';
import grapesjs, { Editor } from 'grapesjs';
import { exportEditorToHtml } from '@packages/email/export-editor-to-html';
import emailPlugin from '@packages/email';
import { ToastService } from '@services/toast.service';

interface Form {
  name: FormControl<string | null>;
  whitelabel: FormControl<boolean>;
  subject: FormControl<string | null>;
}

enum Tab {
  GENERAL = 'GENERAL',
  TEMPLATE = 'TEMPLATE',
}

@Component({
  selector: 'app-detail',
  standalone: true,
  imports: [
    RouterLink,
    TranslocoDirective,
    TranslocoPipe,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './detail.component.html',
  styleUrl: './detail.component.scss',
})
export class DetailComponent implements OnInit {
  @Input({ alias: 'id' }) emailId: string | undefined;
  public email: Email | undefined;
  public loading: boolean = false;
  public activeTab: Tab = Tab.TEMPLATE;
  public tabs = Tab;
  public form: FormGroup<Form> | undefined;
  public saveLoading: boolean = false;
  public showLegend: boolean = false;
  public editor?: Editor;

  constructor(
    private emailService: EmailService,
    private destroyRef: DestroyRef,
    private router: Router,
    private ngZone: NgZone,
    private toastService: ToastService,
  ) {}

  public ngOnInit(): void {
    this.loadEmail();
  }

  public navigateTab(tab: Tab): void {
    this.activeTab = tab;
  }

  private loadEmail(): void {
    if (!this.emailId) {
      this.initForm();
      return;
    }

    this.loading = true;

    this.emailService
      .show(this.emailId)
      .pipe(
        tap((response) => {
          this.email = response.data;
          this.initForm();
          this.initBuilder();
          this.loading = false;
        }),
        catchError((err) => {
          this.toastService.error();
          this.initForm();
          this.initBuilder();
          this.loading = false;
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public submit(): void {
    if (this.saveLoading || !this.form || this.form.invalid || !this.email) {
      this.form?.markAllAsTouched();
      return;
    }

    if (!this.form || !this.email) {
      return;
    }

    let html = this.email.html;

    if (this.editor) {
      html = exportEditorToHtml(this.editor);
    }

    const body: EmailRequest = {
      name: this.form.controls.name.value,
      whitelabel: this.form.controls.whitelabel.value,
      html: html ?? '',
      subject: this.form.controls.subject.value,
    };

    this.saveLoading = true;

    this.emailService
      .update(this.email, body as EmailRequest)
      .pipe(
        tap(() => {
          this.saveLoading = false;
          this.toastService.success();
          this.router.navigate(['emails']);
        }),
        catchError((err) => {
          this.saveLoading = false;
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public toggleShowLegend(): void {
    this.showLegend = !this.showLegend;
  }

  private initForm(): void {
    this.form = new FormGroup<Form>({
      name: new FormControl(this.email?.name ?? null),
      whitelabel: new FormControl(this.email?.whitelabel ?? false, {
        nonNullable: true,
      }),
      subject: new FormControl(this.email?.subject ?? null),
    });
  }

  private initBuilder(): void {
    const blocks: string[] = [];

    if (
      this.email?.event ===
      'Packages\\Events\\Events\\Notification\\Recipient\\Link\\ShareLinkEvent'
    ) {
      blocks.push('connections-table');
    }

    if (
      this.email?.event ===
      'Packages\\Events\\Events\\Notification\\Profile\\SendDnsSettingsEvent'
    ) {
      blocks.push('dns-settings-table');
    }

    this.ngZone.runOutsideAngular(() => {
      setTimeout(() => {
        this.editor = grapesjs.init({
          height: '80.5vh',
          storageManager: false,
          container: '#gjs',
          blockManager: {
            appendTo: '#blocks',
          },
          traitManager: {
            appendTo: '#traits-container',
          },
          selectorManager: {
            appendTo: '#styles-container',
          },
          styleManager: {
            appendTo: '#styles-container',
          },
          layerManager: {
            appendTo: '#layers-container',
          },
          components: this.email?.html ?? '',
          panels: {
            defaults: [],
          },
          fromElement: true,
          // @ts-ignore
          plugins: [(editor) => emailPlugin(editor, {}, blocks)],
        });

        this.editor.setComponents(this.email?.html ?? '');
      }, 100);
    });
  }
}
