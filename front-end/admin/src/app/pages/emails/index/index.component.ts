import { Component, DestroyRef, OnInit, signal } from '@angular/core';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { Email } from '@api/email/models/email.interface';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EmailService } from '@api/email/services/email.service';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  tap,
} from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DatePipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastService } from '@services/toast.service';
import { DropdownComponent } from '@components/dropdown/dropdown.component';

@Component({
  selector: 'app-index',
  standalone: true,
  imports: [
    TranslocoDirective,
    FormsModule,
    TranslocoPipe,
    ReactiveFormsModule,
    DatePipe,
    DropdownComponent,
  ],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public response = signal<PaginatedResponse<Email> | null>(null);
  public loading = signal<boolean>(false);
  public search: FormControl<string | null> = new FormControl(null);

  constructor(
    private emailService: EmailService,
    private destroyRef: DestroyRef,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private toastService: ToastService,
  ) {}

  public ngOnInit(): void {
    this.listenToSearch();
    this.loadEmails();
  }

  public loadEmails(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.emailService
      .index({
        page,
        search: this.search.value,
      })
      .pipe(
        filter((response): response is PaginatedResponse<Email> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public navigateToDetail(email: Email): void {
    this.router.navigate([email.id], { relativeTo: this.activatedRoute });
  }

  private listenToSearch(): void {
    this.search.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap(() => {
          this.loadEmails();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
