<div *transloco="let t; read: 'pages.emails.index'" class="space-y-4">
  <div class="flex justify-end items-center">
    <div class="form-group !w-64">
      <input
        [formControl]="search"
        [placeholder]="'general.search' | transloco"
      />
    </div>
  </div>

  <table class="min-w-full divide-y divide-gray-200 rounded overflow-hidden">
    <thead class="bg-gray-50">
    <tr>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("table.name") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("table.event") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("table.whitelabel") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("table.updated_at") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("table.updated_by") }}</th>
    </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
    <!-- Example row, replace with @for when adding logic -->
      @if (!loading() && !!response() && (response()?.data?.length ?? 0) > 0) {
        @for (email of response()?.data; track $index) {
          <tr (click)="navigateToDetail(email)" class="cursor-pointer">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ email.name  }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ email.event  }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              @if(email.whitelabel) {
                <i class="fa-regular fa-circle-check text-green-500"></i>
              } @else {
                <i class="fa-regular fa-circle-xmark text-red-500"></i>
              }
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ email.updated_at | date  }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ email.updated_by?.firstname }} {{ email.updated_by?.lastname }}</td>
          </tr>
        }
      } @else if (loading()) {
        <tr>
          <td colspan="5">
            <div class="flex items-center justify-center h-20">
              <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-3xl"></i>
            </div>
          </td>
        </tr>
      } @else if(!loading() && !!response() && response()?.data?.length === 0) {
        <tr>
          <td colspan="5" class="text-center h-20">{{ 'general.empty_placeholder' | transloco }}</td>
        </tr>
      }
    </tbody>
  </table>
</div>

<!--  <main>-->
<!--    <components-table (rowClicked)="navigateToDetail($event)" (pageChange)="loadEmails($event)" [response]="response" [isLoading]="loading" [amountOfHeaders]="5" [rowClickable]="true">-->
<!--      <ng-template #headers>-->
<!--        <th>{{ t('table.name')}}</th>-->
<!--        <th>{{ t('table.event')}}</th>-->
<!--        <th>{{ t('table.whitelabel')}}</th>-->
<!--        <th>{{ t('table.updated_at')}}</th>-->
<!--        <th>{{ t('table.updated_by')}}</th>-->
<!--      </ng-template>-->
<!--      <ng-template #rows let-row>-->
<!--        <td>{{ row.name }}</td>-->
<!--        <td>{{ row.event }}</td>-->
<!--        <td>-->

<!--        </td>-->
<!--        <td>{{ row.updated_at | date }}</td>-->
<!--        <td>{{ row.updated_by?.first_name ?? '' }} {{ row.updated_by?.last_name ?? '' }}</td>-->
<!--      </ng-template>-->
<!--    </components-table>-->

