import { Component, inject, OnInit, signal } from '@angular/core';
import { Account } from '@api/profile/models/account.interface';
import { TranslocoDirective } from '@jsverse/transloco';
import { DatePipe } from '@angular/common';
import { AccountState } from '@app/states/account.state';

@Component({
  selector: 'app-general',
  standalone: true,
  imports: [TranslocoDirective, DatePipe],
  templateUrl: './general.component.html',
})
export class GeneralComponent implements OnInit {
  public account = signal<Account | null>(null);

  private accountState = inject(AccountState);

  public ngOnInit(): void {
    this.account = this.accountState.account;
  }
}
