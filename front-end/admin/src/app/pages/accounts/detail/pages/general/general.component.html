<dl *transloco="let t; read: 'pages.accounts.detail.general'" class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
  @if(account(); as account) {
    <div class="sm:col-span-1">
      <dt class="text-sm font-medium text-gray-500">{{  t('stripe_id') }}</dt>
      <dd class="mt-1 text-sm text-gray-900">{{ account?.stripe_id }}</dd>
    </div>
    <div class="sm:col-span-1">
      <dt class="text-sm font-medium text-gray-500">{{ t('billing_email')}}</dt>
      <dd class="mt-1 text-sm text-gray-900">{{ account?.stripe_information?.email }}</dd>
    </div>
    <div class="sm:col-span-1">
      <dt class="text-sm font-medium text-gray-500">{{ t('address') }}</dt>
      <dd class="mt-1 text-sm text-gray-900">
        <p>{{ account?.stripe_information?.street }} {{ account?.stripe_information?.house_number }}
          @if(account?.stripe_information?.house_number_addition) {
          - {{account?.stripe_information?.house_number_addition}}
          }
        </p>
        <p>{{ account?.stripe_information?.zipcode }}, {{account?.stripe_information?.city}}</p>
        <p>{{ account?.stripe_information?.country }}</p>
      </dd>
    </div>
    <div class="sm:col-span-1">
      <dt class="text-sm font-medium text-gray-500">{{ t('verified_at') }}</dt>
      <dd class="mt-1 text-sm text-gray-900">{{ account?.verified_at | date }}</dd>
    </div>
  }
</dl>
