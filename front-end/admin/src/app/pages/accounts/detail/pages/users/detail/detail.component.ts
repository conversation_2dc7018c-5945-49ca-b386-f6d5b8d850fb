import {
  Component,
  DestroyRef,
  inject,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { Location } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { User } from '@api/profile/models/user.interface';
import { Account } from '@api/profile/models/account.interface';
import { UserRequest } from '@api/profile/requests/user.request';
import { UserService } from '@api/profile/services/user.service';
import { UserStoreRequest } from '@api/profile/requests/user-store.request';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AccountState } from '@app/states/account.state';
import { ToastService } from '@services/toast.service';
import { UserRole } from '@api/profile/enums/user-role.enum';

interface Form {
  firstname: FormControl<string | null>;
  lastname: FormControl<string | null>;
  email: FormControl<string | null>;
  role: FormControl<UserRole>;
}

@Component({
  selector: 'app-detail',
  standalone: true,
  imports: [TranslocoPipe, TranslocoDirective, ReactiveFormsModule],
  templateUrl: './detail.component.html',
})
export class DetailComponent implements OnInit {
  @Input({ alias: 'id' }) id!: string;
  public account = signal<Account | null>(null);
  public user = signal<User | null>(null);
  public form: FormGroup<Form> = new FormGroup<Form>({
    firstname: new FormControl(null, [Validators.required]),
    lastname: new FormControl(null, [Validators.required]),
    email: new FormControl(null, [Validators.required]),
    role: new FormControl(UserRole.USER, {
      validators: [Validators.required],
      nonNullable: true,
    }),
  });
  public loading = signal<boolean>(false);
  public userLoading = signal<boolean>(false);

  public readonly role = UserRole;

  private location = inject(Location);
  private accountState = inject(AccountState);
  private userService = inject(UserService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public ngOnInit(): void {
    this.account = this.accountState.account;

    this.loadUser();
  }

  public navigateBack(): void {
    this.location.back();
  }

  public submit(): void {
    const account = this.account();

    if (!account || this.loading() || !this.form || this.form.invalid) {
      this.form?.markAllAsTouched();
      return;
    }

    let body: UserRequest | UserStoreRequest = {
      firstname: this.form.controls.firstname.value as string,
      lastname: this.form.controls.lastname.value as string,
      role: this.form.controls.role.value,
    };

    let api = null;

    const user = this.user();

    if (user) {
      api = this.userService.update(account, user, body);
    } else {
      body = {
        ...body,
        email: this.form.controls.email.value ?? '',
      };

      api = this.userService.store(account, body);
    }

    this.loading.set(true);

    api
      .pipe(
        tap((response) => {
          this.toastService.success();
          this.navigateBack();
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.error();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadUser(): void {
    if (this.id === 'add') {
      return;
    }

    const account = this.account();

    if (!account) {
      return;
    }

    this.userLoading.set(true);

    this.userService
      .show(account, this.id)
      .pipe(
        tap((response) => {
          this.user.set(response.data);
          this.userLoading.set(false);

          this.form.patchValue({
            firstname: response.data.firstname,
            lastname: response.data.lastname,
            email: response.data.email,
            role: response.data.role,
          });

          this.form.controls.email.disable();
        }),
        catchError((err) => {
          this.toastService.error();
          this.userLoading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
