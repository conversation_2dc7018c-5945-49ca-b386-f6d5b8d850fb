import { Component, DestroyRef, inject, signal } from '@angular/core';
import { ModalComponent } from '@components/modal/modal.component';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { Account } from '@api/profile/models/account.interface';
import { User } from '@api/profile/models/user.interface';
import { catchError, Subject, tap } from 'rxjs';
import { UserService } from '@api/profile/services/user.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Clipboard } from '@angular/cdk/clipboard';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [ModalComponent, TranslocoDirective, TranslocoPipe],
  templateUrl: './reset-password.component.html',
})
export class ResetPasswordComponent {
  public account: Account | undefined;
  public user: User | undefined;
  public close$: Subject<void> = new Subject();
  public loading = signal<boolean>(false);
  public password = signal<string | null>(null);
  public copied = signal<boolean>(false);

  private userService = inject(UserService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private clipboard = inject(Clipboard);

  public close(): void {
    this.close$.next();
    this.close$.complete();
  }

  public submit(): void {
    if (!this.user || !this.account || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.userService
      .resetPassword(this.account, this.user)
      .pipe(
        tap((response) => {
          this.password.set(response.data.password);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.error();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public copyToClipboard(): void {
    this.clipboard.copy(this.password() ?? '');

    this.copied.set(true);

    setTimeout(() => {
      this.copied.set(false);
    }, 3000);
  }
}
