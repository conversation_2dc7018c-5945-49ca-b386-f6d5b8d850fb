import { Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { Account } from '@api/profile/models/account.interface';
import { User } from '@api/profile/models/user.interface';
import { DeleteComponent } from '@pages/accounts/detail/pages/users/index/delete/delete.component';
import { take, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DeleteService {
  constructor(private modalService: ModalService) {}

  public show(account: Account, user: User): Promise<boolean> {
    const modal = this.modalService.attach(DeleteComponent);

    modal.componentRef.instance.account = account;
    modal.componentRef.instance.user = user;

    return new Promise((resolve) => {
      modal.componentRef.instance.close$
        .pipe(
          tap((value) => {
            resolve(value);
            modal.overlayRef.detach();
          }),
          take(1),
        )
        .subscribe();
    });
  }
}
