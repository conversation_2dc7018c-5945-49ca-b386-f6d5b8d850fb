<components-modal (close$)="close()">
  <div *transloco="let t; read: 'pages.accounts.detail.users.index.delete'" class="space-y-4">
    <h2 class="text-lg font-medium">{{ t('title', {name: user?.firstname + ' ' + user?.lastname}) }}</h2>
    <p>{{ t('description') }}</p>

    <div class="flex justify-end space-x-4">
      <button (click)="close()" type="button" class="link">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()" [class.loading]="loading()" class="btn --danger">{{ 'general.buttons.delete' | transloco }}</button>
    </div>
  </div>

</components-modal>
