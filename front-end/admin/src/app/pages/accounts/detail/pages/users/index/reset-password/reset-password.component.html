<components-modal *transloco="let t; read: 'pages.accounts.detail.users.index.reset-password'" (close$)="close()">
  @if(!password()) {
  <div class="space-y-4">
    <h2 class="text-lg font-medium">{{ t('title') }}</h2>
    <p>{{ t('description', {name: user?.firstname}) }}</p>

    <div class="flex justify-end space-x-4">
      <button (click)="close()" type="button" class="link">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()" [class.loading]="loading()" class="btn --light-blue">{{ 'general.buttons.reset' | transloco }}</button>
    </div>
  </div>
  } @else {
    <div class="space-y-4">
      <h2 class="text-lg font-medium">{{ t('title') }}</h2>

      <div (click)="copyToClipboard()" class="w-full flex justify-between items-center px-4 py-2 border border-localium-gray-400 rounded font-light bg-white ring-localium-gray-400 outline-localium-gray-400 text-gray-400 space-x-2 cursor-copy">
        <p class="w-full truncate !select-none cursor-copy">{{ password() }}</p>
        <div class="relative">
          <span><i class="fa-light fa-clipboard text-xl"></i></span>
          <span [class.opacity-0]="!copied()" [class.opacity-100]="copied()" class="absolute top-0 left-0 transition ease-in-out duration-200"><i class="fa-light fa-clipboard-check text-xl"></i></span>
        </div>
      </div>
    </div>
  }

</components-modal>
