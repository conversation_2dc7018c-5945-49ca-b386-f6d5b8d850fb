<div class="space-y-4">
  <div class="flex justify-between items-center">
    <div>
      <a routerLink="add" class="btn --light-blue">
        <i class="fa-regular fa-plus"></i>
        <span>{{ 'general.buttons.add' | transloco }}</span>
      </a>
    </div>
    <div class="form-group-new !w-64">
      <input [formControl]="search" [placeholder]="'general.search' | transloco" type="text">
    </div>
  </div>

  <table *transloco="let t; read: 'pages.accounts.detail.users.index'" class="min-w-full divide-y divide-gray-200 rounded overflow-hidden">
    <thead class="bg-gray-50">
    <tr>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("name") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("email") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("role") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("last_login_at") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("verified_at") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"></th>
    </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
    <!-- Example row, replace with @for when adding logic -->
      @if (!loading() && !!response() && (response()?.data?.length ?? 0) > 0) {
        @for (user of response()?.data; track $index) {
          <tr [routerLink]="user.id" class="cursor-pointer">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.firstname }} {{ user.lastname }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.email }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ 'enums.user-role.' + user.role | transloco }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.last_login_at | date }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.verified_at | date }}</td>
            <td>
              <components-dropdown width="w-48">
                <button (click)="impersonate(user); $event.stopPropagation()" class="btn-dropdown">
                  <i class="fa-light fa-user-secret"></i>
                  <span>{{ t('impersonate') }}</span>
                </button>
                <button (click)="resetPassword(user); $event.stopPropagation()" class="btn-dropdown">
                  <i class="fa-regular fa-rotate-right"></i>
                  <span>{{ t('reset_password') }}</span>
                </button>
                <button (click)="delete(user); $event.stopPropagation()" class="btn-dropdown">
                  <i class="fa-regular fa-trash-can"></i>
                  <span>{{ 'general.buttons.delete' | transloco }}</span>
                </button>
              </components-dropdown>
            </td>
          </tr>
        }
      } @else if (loading()) {
        <tr>
          <td colspan="6">
            <div class="flex items-center justify-center h-20">
              <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-3xl"></i>
            </div>
          </td>
        </tr>
      } @else if(!loading() && !!response() && response()?.data?.length === 0) {
        <tr>
          <td colspan="6" class="text-center h-20">{{ 'general.empty_placeholder' | transloco }}</td>
        </tr>
      }
    </tbody>
  </table>
