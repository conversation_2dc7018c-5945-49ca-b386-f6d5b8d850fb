import { Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { Account } from '@api/profile/models/account.interface';
import { User } from '@api/profile/models/user.interface';
import { ResetPasswordComponent } from '@pages/accounts/detail/pages/users/index/reset-password/reset-password.component';
import { take, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ResetPasswordService {
  constructor(private modalService: ModalService) {}

  public show(account: Account, user: User): void {
    const modal = this.modalService.attach(ResetPasswordComponent);

    modal.componentRef.instance.account = account;
    modal.componentRef.instance.user = user;

    modal.componentRef.instance.close$
      .pipe(
        tap(() => {
          modal.overlayRef.detach();
        }),
        take(1),
      )
      .subscribe();
  }
}
