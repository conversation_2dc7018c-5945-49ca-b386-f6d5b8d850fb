<table *transloco="let t; read: 'pages.accounts.detail.invoices'" class="min-w-full divide-y divide-gray-200 rounded overflow-hidden">
  <thead class="bg-gray-50">
  <tr>
    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('number') }}</th>
    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('status') }}</th>
    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('total_including_vat') }}</th>
    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('total_excluding_vat') }}</th>
    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('date') }}</th>
    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"></th>
  </tr>
  </thead>
  <tbody class="bg-white divide-y divide-gray-200">
  <!-- Example row, replace with @for when adding logic -->
    @if (!loading() && !!response() && (response()?.data?.length ?? 0) > 0) {
      @for (invoice of response()?.data; track $index) {
        <tr class="cursor-pointer">
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ invoice.number }}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            <span
              [ngClass]="{
              'bg-green-500': invoice.status === invoiceStatus.OPEN,
              'bg-gray-500': invoice.status === invoiceStatus.VOID,
              'bg-red-500': invoice.status === invoiceStatus.UNCOLLECTIBLE,
              'bg-purple-500': invoice.status === invoiceStatus.DRAFT,
              'bg-blue-500': invoice.status === invoiceStatus.PAID
            }"
              class="px-4 py-2 text-white rounded"
            >
            {{ t('statuses.' + invoice.status) }}
          </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ (invoice.total_including_vat ?? 0) / 100 | currency }}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ (invoice.total_excluding_vat ?? 0) / 100 | currency }}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ invoice.invoiced_date | date }}</td>
          <td>
            <components-dropdown>
              @if(invoice.stripe_id) {
                <a [href]="configService.config?.stripe?.dashboard_url + '/invoices/' + invoice.stripe_id" target="_blank" class="btn-dropdown bg-[#675DFF] text-white">
                  <i class="fa-light fa-arrow-up-right-from-square"></i>
                  <span>{{ t('stripe') }}</span>
                </a>
              }
            </components-dropdown>
          </td>
        </tr>
      }
    } @else if (loading()) {
      <tr>
        <td colspan="6">
          <div class="flex items-center justify-center h-20">
            <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-3xl"></i>
          </div>
        </td>
      </tr>
    } @else if(!loading() && !!response() && response()?.data?.length === 0) {
      <tr>
        <td colspan="6" class="text-center h-20">{{ 'general.empty_placeholder' | transloco }}</td>
      </tr>
    }
  </tbody>
</table>
