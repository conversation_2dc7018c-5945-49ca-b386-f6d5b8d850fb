<div *transloco="let t; read: 'pages.accounts.detail.users.detail'" class="space-y-4 h-full">
  <div class="flex justify-between items-center">
    <button (click)="navigateBack()" class="link space-x-2">
      <i class="fa-regular fa-arrow-left"></i>
      <span>{{ 'general.back' | transloco }}</span>
    </button>

    @if(form && !userLoading()) {
      <button (click)="submit()" [class.--loading]="loading()" class="btn --light-blue">
        <i class="fa-regular fa-floppy-disk"></i>
        <span>{{ 'general.buttons.save' | transloco }}</span>
      </button>
    }
  </div>

  @if(form && !userLoading()) {
    <form [formGroup]="form" class="grid grid-cols-1 gap-x-8 gap-y-10 border-b-0 border-gray-900/10 pb-12 md:grid-cols-3">
      <div>
        <h2 class="text-base font-semibold leading-7 text-gray-900">{{ t('general.title') }}</h2>
        <p class="mt-1 text-sm leading-6 text-gray-600">{{ t('general.description')}}</p>
      </div>

      <div class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2 md:col-span-2">
        <div class="form-group-new">
          <label>{{ t('general.firstname') }}</label>
          <input [formControl]="form.controls.firstname" type="text">
        </div>

        <div class="form-group-new">
          <label>{{ t('general.lastname') }}</label>
          <input [formControl]="form.controls.lastname" type="text">
        </div>

        <div class="form-group-new">
          <label>{{ t('general.email') }}</label>
          <input [formControl]="form.controls.email" type="email">
        </div>

        <div class="form-group-new">
          <label>{{ t('general.role') }}</label>
          <select [formControl]="form.controls.role">
            <option [ngValue]="role.ADMIN">{{ 'enums.user-role.' + role.ADMIN | transloco }}</option>
            <option [ngValue]="role.USER">{{ 'enums.user-role.' + role.USER | transloco }}</option>
          </select>
        </div>
      </div>
    </form>
  } @else {
    <div class="flex items-center justify-center h-[80%]">
      <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-4xl"></i>
    </div>
  }
</div>
