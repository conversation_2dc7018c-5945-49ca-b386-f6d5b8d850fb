import { Component, DestroyRef, inject, signal } from '@angular/core';
import { ModalComponent } from '@components/modal/modal.component';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { User } from '@api/profile/models/user.interface';
import { UserService } from '@api/profile/services/user.service';
import { catchError, Subject, tap } from 'rxjs';
import { Account } from '@api/profile/models/account.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-delete',
  standalone: true,
  imports: [ModalComponent, TranslocoDirective, TranslocoPipe],
  templateUrl: './delete.component.html',
})
export class DeleteComponent {
  public account: Account | undefined;
  public user: User | undefined;
  public close$: Subject<boolean> = new Subject();
  public loading = signal<boolean>(false);

  private userService = inject(UserService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public close(value: boolean = false): void {
    this.close$.next(value);
    this.close$.complete();
  }

  public submit(): void {
    if (!this.user || !this.account || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.userService
      .delete(this.account, this.user)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.close(true);
          this.toastService.success();
        }),
        catchError((err) => {
          this.toastService.error();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
