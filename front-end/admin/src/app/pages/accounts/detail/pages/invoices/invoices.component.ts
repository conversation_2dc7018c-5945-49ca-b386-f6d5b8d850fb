import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { Account } from '@api/profile/models/account.interface';
import { catchError, filter, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CurrencyPipe, DatePipe, NgClass } from '@angular/common';
import { ConfigService } from '@services/config.service';
import { AccountState } from '@app/states/account.state';
import { Invoice } from '@api/billing/models/invoice.interface';
import { InvoiceStatus } from '@api/billing/enums/invoice-status.enum';
import { InvoiceService } from '@api/billing/services/invoice.service';
import { ToastService } from '@services/toast.service';
import { DropdownComponent } from '@components/dropdown/dropdown.component';

@Component({
  selector: 'app-invoices',
  standalone: true,
  imports: [
    TranslocoDirective,
    NgClass,
    CurrencyPipe,
    DatePipe,
    DropdownComponent,
    TranslocoPipe,
  ],
  templateUrl: './invoices.component.html',
})
export class InvoicesComponent implements OnInit {
  public response = signal<PaginatedResponse<Invoice> | null>(null);
  public loading = signal<boolean>(false);
  public account = signal<Account | null>(null);

  public readonly invoiceStatus = InvoiceStatus;

  private invoiceService = inject(InvoiceService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private accountState = inject(AccountState);
  public configService = inject(ConfigService);

  public ngOnInit(): void {
    this.account = this.accountState.account;

    this.loadInvoices();
  }

  public loadInvoices(page: number = 1): void {
    const account = this.account();

    if (!account || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.invoiceService
      .index(account, { page })
      .pipe(
        filter((response): response is PaginatedResponse<Invoice> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
