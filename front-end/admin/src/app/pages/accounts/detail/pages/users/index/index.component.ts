import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { Account } from '@api/profile/models/account.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { User } from '@api/profile/models/user.interface';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  tap,
} from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router, RouterLink } from '@angular/router';
import { UserService } from '@api/profile/services/user.service';
import { DeleteService } from '@pages/accounts/detail/pages/users/index/delete/delete.service';
import { ResetPasswordService } from '@pages/accounts/detail/pages/users/index/reset-password/reset-password.service';
import { AccountState } from '@app/states/account.state';
import { ToastService } from '@services/toast.service';
import { DatePipe } from '@angular/common';
import { DropdownComponent } from '@components/dropdown/dropdown.component';

@Component({
  selector: 'app-index',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    RouterLink,
    TranslocoPipe,
    DatePipe,
    DropdownComponent,
    TranslocoDirective,
  ],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public account = signal<Account | null>(null);
  public loading = signal<boolean>(false);
  public response = signal<PaginatedResponse<User> | null>(null);
  public search: FormControl<string | null> = new FormControl(null);

  private accountState = inject(AccountState);
  private userService = inject(UserService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private deleteService = inject(DeleteService);
  private resetPasswordService = inject(ResetPasswordService);

  public ngOnInit(): void {
    this.account = this.accountState.account;
    this.loadUsers();
    this.listenToSearch();
  }

  public loadUsers(page: number = 1): void {
    const account = this.account();

    if (!account || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.userService
      .index(account, { page, search: this.search.value })
      .pipe(
        filter((response): response is PaginatedResponse<User> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.error();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public async delete(user: User): Promise<void> {
    const account = this.account();

    if (!account) {
      return;
    }

    const result = await this.deleteService.show(account, user);

    if (!result) {
      return;
    }

    this.loadUsers(this.response()?.meta.current_page ?? 1);
  }

  public resetPassword(user: User): void {
    const account = this.account();

    if (!account) {
      return;
    }

    this.resetPasswordService.show(account, user);
  }

  public impersonate(user: User): void {
    const account = this.account();

    if (!account) {
      return;
    }

    this.userService
      .impersonate(account, user)
      .pipe(
        tap((response) => {
          window.open(response.data.url, '_blank');
        }),
        catchError((err) => {
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private listenToSearch(): void {
    this.search.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap(() => {
          this.loadUsers();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
