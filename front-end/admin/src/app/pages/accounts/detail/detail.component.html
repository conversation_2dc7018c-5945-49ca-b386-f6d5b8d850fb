<div *transloco="let t; read: 'pages.accounts.detail'" class="space-y-6">
  <div class="flex items-center justify-between">
    <h1 class="text-3xl font-bold leading-tight tracking-tight text-gray-900">{{ account()?.name }}</h1>
    <button
      (click)="toggleVerification()"
      [ngClass]="{
        '--success': !account()?.verified_at,
        '--danger': account()?.verified_at
      }"
      class="btn --small --success">
      <i class="fa-regular fa-power-off"></i>
      <span>{{ t('buttons.' + (account()?.verified_at ? 'block' : 'activate')) }}</span>
    </button>
  </div>

  <div class="space-y-8 h-full">
    <div>
      <div class="border-b border-gray-200">
        <div>
          <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <a
              [routerLink]="['/accounts/', account()?.id]"
              [routerLinkActiveOptions]="{exact: true}"
              routerLinkActive="!border-eaglo-blue !text-gray-900"
              class="whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 space-x-2"
            >
              <i class="fa-regular fa-circle-info"></i>
              <span>{{ t('nav.general') }}</span>
            </a>
            <a
              routerLink="users"
              routerLinkActive="!border-eaglo-blue !text-gray-900"
              class="whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 space-x-2"
            >
              <i class="fa-regular fa-users"></i>
              <span>{{ t('nav.users') }}</span>
            </a>
            <a
              routerLink="invoices"
              routerLinkActive="!border-eaglo-blue !text-gray-900"
              class="whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 space-x-2"
            >
              <i class="fa-regular fa-file-invoice-dollar"></i>
              <span>{{ t('nav.invoices') }}</span>
            </a>
          </nav>
        </div>
      </div>
    </div>

    <div class="h-full">
      <router-outlet/>
    </div>
  </div>
</div>
