import { Component, inject, OnInit, signal } from '@angular/core';
import { TranslocoDirective } from '@jsverse/transloco';
import { Account } from '@api/profile/models/account.interface';
import { formatDate, NgClass } from '@angular/common';
import { RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { AccountState } from '@app/states/account.state';
import { ToggleVerificationService } from '@pages/accounts/index/toggle-verification/toggle-verification.service';

@Component({
  selector: 'app-detail',
  imports: [
    TranslocoDirective,
    NgClass,
    RouterLink,
    RouterLinkActive,
    RouterOutlet,
  ],
  templateUrl: './detail.component.html',
})
export class DetailComponent implements OnInit {
  public account = signal<Account | null>(null);

  private accountState = inject(AccountState);
  private toggleVerificationService = inject(ToggleVerificationService);

  public ngOnInit(): void {
    this.account = this.accountState.account;
  }

  public async toggleVerification(): Promise<void> {
    const account = this.account();

    if (!account) {
      return;
    }

    const result = await this.toggleVerificationService.show(account);

    if (!result) {
      return;
    }

    if (account.verified_at) {
      account.verified_at = null;
    } else {
      account.verified_at = formatDate(new Date(), 'yyyy-MM-dd', 'en');
    }

    this.account.update((state) => (state ? account : null));
  }
}
