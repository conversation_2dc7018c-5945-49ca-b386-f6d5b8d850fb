import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { Account } from '@api/profile/models/account.interface';
import { DropdownComponent } from '@components/dropdown/dropdown.component';
import { DatePipe, NgClass } from '@angular/common';
import { AccountService } from '@api/profile/services/account.service';
import { ToastService } from '@services/toast.service';
import { catchError, filter, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ConfigService } from '@services/config.service';
import { InviteService } from '@pages/accounts/index/invite/invite.service';
import { ToggleVerificationService } from '@pages/accounts/index/toggle-verification/toggle-verification.service';
import { DeleteService } from '@pages/accounts/index/delete/delete.service';

@Component({
  selector: 'app-index',
  imports: [
    TranslocoDirective,
    ReactiveFormsModule,
    DropdownComponent,
    TranslocoPipe,
    NgClass,
    DatePipe,
  ],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public loading = signal<boolean>(false);
  public response = signal<PaginatedResponse<Account> | null>(null);

  public search: FormControl<string | null> = new FormControl(null);

  private router = inject(Router);
  private accountService = inject(AccountService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private inviteService = inject(InviteService);
  private toggleVerificationService = inject(ToggleVerificationService);
  private deleteService = inject(DeleteService);
  public configService = inject(ConfigService);

  public ngOnInit(): void {
    this.loadAccounts();
  }

  public navigateToDetail(account: Account): void {
    this.router.navigateByUrl(`/accounts/${account.id}`);
  }

  public loadAccounts(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.accountService
      .index({
        page,
        search: this.search.value,
      })
      .pipe(
        filter((response): response is PaginatedResponse<Account> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.error();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public async toggleVerification(account: Account): Promise<void> {
    const result = await this.toggleVerificationService.show(account);

    if (!result) {
      return;
    }

    this.loadAccounts(this.response()?.meta.current_page ?? 1);
  }

  public openInvite(): void {
    this.inviteService.show();
  }

  public async delete(account: Account): Promise<void> {
    const result = await this.deleteService.show(account);

    if (!result) {
      return;
    }

    this.loadAccounts();
  }
}
