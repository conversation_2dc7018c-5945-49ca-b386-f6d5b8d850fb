import { Component, DestroyRef, inject, signal } from '@angular/core';
import { Account } from '@api/profile/models/account.interface';
import { catchError, Subject, tap } from 'rxjs';
import { AccountService } from '@api/profile/services/account.service';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ModalComponent } from '@components/modal/modal.component';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-delete',
  standalone: true,
  imports: [ModalComponent, TranslocoDirective, TranslocoPipe],
  templateUrl: './delete.component.html',
})
export class DeleteComponent {
  public account?: Account;
  public close$: Subject<boolean> = new Subject();
  public loading = signal<boolean>(false);

  private accountService = inject(AccountService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public close(value: boolean = false): void {
    this.close$.next(value);
    this.close$.complete();
  }

  public submit(): void {
    if (!this.account || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.accountService
      .delete(this.account)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.toastService.success();
          this.close(true);
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
