<components-modal (close$)="close()">
  <div *transloco="let t; read: 'pages.accounts.index.delete'" class="space-y-4">
    <div class="space-y-2">
      <h2 class="text-lg font-medium">{{ t('title', {name: account?.name}) }}</h2>
      <p>{{ t('description', {name: account?.name}) }}</p>
    </div>

    <div class="flex justify-end space-x-4">
      <button (click)="close()" type="button" class="link">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()" [class.loading]="loading()" class="btn --danger">{{ 'general.buttons.delete' | transloco }}</button>
    </div>
  </div>
</components-modal>
