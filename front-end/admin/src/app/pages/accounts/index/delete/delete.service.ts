import { Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { Account } from '@api/profile/models/account.interface';
import { DeleteComponent } from '@pages/accounts/index/delete/delete.component';
import { take, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DeleteService {
  constructor(private modalService: ModalService) {}

  public show(account: Account): Promise<boolean> {
    const modal = this.modalService.attach(DeleteComponent);

    modal.componentRef.instance.account = account;

    return new Promise<boolean>((resolve) => {
      modal.componentRef.instance.close$
        .pipe(
          tap((value) => {
            resolve(value);
            modal.overlayRef.detach();
          }),
          take(1),
        )
        .subscribe();
    });
  }
}
