<div *transloco="let t; read: 'pages.accounts.index'" class="space-y-4">
  <div class="flex justify-between items-center">
    <button (click)="openInvite()" class="btn --blue --large">
      <i class="fa-regular fa-plus"></i>
      <span>{{ t('invite') }}</span>
    </button>
    <div class="form-group !w-64">
      <input
        [formControl]="search"
        [placeholder]="'general.search' | transloco"
      />
    </div>
  </div>

  <table class="min-w-full divide-y divide-gray-200 rounded overflow-hidden">
    <thead class="bg-gray-50">
    <tr>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("table.name") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("table.product") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("table.status") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t("table.last_login_at") }}</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"></th>
    </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
    <!-- Example row, replace with @for when adding logic -->
      @if (!loading() && !!response() && (response()?.data?.length ?? 0) > 0) {
        @for (account of response()?.data; track $index) {
          <tr (click)="navigateToDetail(account)" class="cursor-pointer">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex items-center space-x-2">
              <i
                [ngClass]="{
              'text-red-500': !account.verified_at,
              'text-green-500': account.verified_at
            }"
                class="fa-regular fa-badge-check"></i>
              <p>{{ account.name }}</p>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ account.active_subscription?.product?.name }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ account.active_subscription?.status }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ account.last_login_at | date }}</td>
            <td>
              <components-dropdown>
                @if(account.stripe_id) {
                  <a [href]="configService.config?.stripe?.dashboard_url + '/customers/' + account.stripe_id" class="btn-dropdown">
                    <i class="fa-regular fa-arrow-up-right-from-square text-sm"></i>
                    <span>{{ t('buttons.stripe') }}</span>
                  </a>
                }
                <button (click)="navigateToDetail(account)" class="btn-dropdown">
                  <i class="fa-regular fa-pen"></i>
                  <span>{{  'general.buttons.edit' | transloco }}</span>
                </button>

                <button
                  (click)="toggleVerification(account); $event.stopPropagation()"
                  [ngClass]="{
                    '!text-green-500': !account.verified_at,
                    '!text-red-500': account.verified_at
                  }"
                  class="btn-dropdown">
                  <i class="fa-regular fa-power-off"></i>
                  <span>{{ t('buttons.' + (account.verified_at ? 'block' : 'activate')) }}</span>
                </button>
                <button (click)="delete(account)" class="btn-dropdown">
                  <i class="fa-regular fa-trash-can"></i>
                  <span>{{ 'general.buttons.delete' | transloco }}</span>
                </button>
              </components-dropdown>
            </td>
          </tr>
        }
      } @else if (loading()) {
        <tr>
          <td colspan="5">
            <div class="flex items-center justify-center h-20">
              <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-3xl"></i>
            </div>
          </td>
        </tr>
      } @else if(!loading() && !!response() && response()?.data?.length === 0) {
        <tr>
          <td colspan="5" class="text-center h-20">{{ 'general.empty_placeholder' | transloco }}</td>
        </tr>
      }
    </tbody>
  </table>
</div>
