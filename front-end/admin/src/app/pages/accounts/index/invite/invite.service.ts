import { Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { InviteComponent } from '@pages/accounts/index/invite/invite.component';
import { take, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class InviteService {
  constructor(private modalService: ModalService) {}

  public show(): void {
    const modal = this.modalService.attach(InviteComponent);

    modal.componentRef.instance.close$
      .pipe(
        tap(() => {
          modal.overlayRef.detach();
        }),
        take(1),
      )
      .subscribe();
  }
}
