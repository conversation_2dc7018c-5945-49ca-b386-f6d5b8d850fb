<components-modal (close$)="close()">
  <div *transloco="let t; read: 'pages.accounts.index.invite-modal'" class="space-y-4">
    <div class="space-y-1">
      <h2 class="text-lg font-medium">{{ t('title') }}</h2>
      <p>{{ t('description') }}</p>
    </div>

    @if(form) {
      <div [class.ng-submitted]="submitted()" class="grid grid-cols-2 gap-4">
        <div class="form-group-new">
          <label>{{ t('firstname') }}</label>
          <input [formControl]="form.controls.firstname">
        </div>

        <div class="form-group-new">
          <label>{{ t('lastname') }}</label>
          <input [formControl]="form.controls.lastname">
        </div>

        <div class="form-group-new col-span-2">
          <label>{{ t('email') }}</label>
          <input [formControl]="form.controls.email">
        </div>
      </div>
    }

    <div class="flex justify-end space-x-4">
      <button (click)="close()" type="button" class="link">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()" [class.--loading]="loading()" class="btn --blue">{{ 'general.buttons.submit' | transloco }}</button>
    </div>
  </div>
</components-modal>
