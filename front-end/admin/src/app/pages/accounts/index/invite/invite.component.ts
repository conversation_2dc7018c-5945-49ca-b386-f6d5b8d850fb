import { Component, DestroyRef, signal } from '@angular/core';
import { catchError, Subject, tap } from 'rxjs';
import { AccountService } from '@api/profile/services/account.service';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AccountInviteRequest } from '@api/profile/requests/account-invite.request';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ModalComponent } from '@components/modal/modal.component';

interface Form {
  firstname: FormControl<string | null>;
  lastname: FormControl<string | null>;
  email: FormControl<string | null>;
}

@Component({
  selector: 'app-invite',
  standalone: true,
  imports: [
    ModalComponent,
    TranslocoDirective,
    TranslocoPipe,
    ReactiveFormsModule,
  ],
  templateUrl: './invite.component.html',
})
export class InviteComponent {
  public close$: Subject<void> = new Subject<void>();
  public loading = signal<boolean>(false);
  public submitted = signal<boolean>(false);
  public form: FormGroup<Form> = new FormGroup<Form>({
    firstname: new FormControl(null, [Validators.required]),
    lastname: new FormControl(null, [Validators.required]),
    email: new FormControl(null, [Validators.required, Validators.email]),
  });

  constructor(
    private accountService: AccountService,
    private translocoService: TranslocoService,
    private toastService: HotToastService,
    private destroyRef: DestroyRef,
  ) {}

  public submit(): void {
    this.submitted.set(true);
    if (this.form.invalid || this.loading()) {
      this.form.markAllAsTouched();
      return;
    }

    this.loading.set(true);

    this.accountService
      .invite(this.form.value as AccountInviteRequest)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.toastService.success();
          this.close();
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public close(): void {
    this.close$.next();
    this.close$.complete();
  }
}
