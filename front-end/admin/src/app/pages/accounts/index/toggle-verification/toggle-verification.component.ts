import { Component, DestroyRef, inject, signal } from '@angular/core';
import { Account } from '@api/profile/models/account.interface';
import { AccountService } from '@api/profile/services/account.service';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { catchError, Subject, tap } from 'rxjs';
import { ModalComponent } from '@components/modal/modal.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NgClass } from '@angular/common';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-toggle-verification',
  standalone: true,
  imports: [ModalComponent, TranslocoDirective, TranslocoPipe, NgClass],
  templateUrl: './toggle-verification.component.html',
})
export class ToggleVerificationComponent {
  public account: Account | undefined;
  public close$: Subject<boolean> = new Subject();

  public loading = signal<boolean>(false);

  private accountService = inject(AccountService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public close(value: boolean = false): void {
    this.close$.next(value);
    this.close$.complete();
  }

  public submit(): void {
    if (!this.account || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.accountService
      .toggleVerification(this.account)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.toastService.success();
          this.close(true);
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
