<components-modal (close$)="close()">
  <div *transloco="let t; read: 'pages.accounts.toggle-verification'">
    <h2 class="text-lg font-medium">{{ t('title.' + (account?.verified_at  ? 'block' : 'activate'), {name: account?.name}) }}</h2>
    <p>{{ t('description.' + (account?.verified_at ? 'block' : 'activate')) }}</p>

    <div class="flex justify-end space-x-4">
      <button (click)="close()" type="button" class="link">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()"
              [ngClass]="{
                'loading': loading(),
                '--danger': account?.verified_at,
                '--success': !account?.verified_at
              }" type="button"
              class="btn"
      >
        {{ 'general.buttons.confirm' | transloco }}
      </button>
    </div>
  </div>

</components-modal>
