import { Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { Account } from '@api/profile/models/account.interface';
import { take, tap } from 'rxjs';
import { ToggleVerificationComponent } from '@pages/accounts/index/toggle-verification/toggle-verification.component';

@Injectable({
  providedIn: 'root',
})
export class ToggleVerificationService {
  constructor(private modalService: ModalService) {}

  public show(account: Account): Promise<boolean> {
    const modal = this.modalService.attach(ToggleVerificationComponent);

    modal.componentRef.instance.account = account;

    return new Promise((resolve) => {
      modal.componentRef.instance.close$
        .pipe(
          tap((value) => {
            resolve(value);
            modal.overlayRef.detach();
          }),
          take(1),
        )
        .subscribe();
    });
  }
}
