import { Routes } from '@angular/router';
import { DetailComponent } from '@pages/accounts/detail/detail.component';
import { accountResolver } from '@app/resolvers/accountResolver';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    loadComponent: () =>
      import('./index/index.component').then((c) => c.IndexComponent),
  },
  {
    path: ':id',
    component: DetailComponent,
    resolve: {
      account: accountResolver,
    },
    loadChildren: () => import('./detail/detail.routes').then((r) => r.routes),
  },
];
