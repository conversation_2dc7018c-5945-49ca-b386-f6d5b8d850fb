<content-container *transloco="let t; read: 'pages.support.index'">
  <header>{{ t('title') }}</header>

  @if(form) {
    <div buttons class="flex space-x-4">
      <div class="w-64">
        <form-connected-select [formControl]="form.controls.accounts" [multiple]="true" [source]="accountsCallback" [placeholder]="t('accounts')"></form-connected-select>
      </div>

      <div class="w-64">
        <form-select
          [formControl]="form.controls.statuses"
          [data]="[
            {value: status.OPEN, label: t('statuses.OPEN')},
            {value: status.IN_PROGRESS, label: t('statuses.IN_PROGRESS')},
            {value: status.PENDING_CLIENT, label: t('statuses.PENDING_CLIENT')},
            {value: status.RESOLVED, label: t('statuses.RESOLVED')},
            {value: status.REOPENED, label: t('statuses.REOPENED')},
            {value: status.CLOSED, label: t('statuses.CLOSED')},
          ]"
          [placeholder]="t('select_statuses')"
          [multiple]="true"
        ></form-select>
      </div>
      <div class="form-group-new w-64">
        <input [formControl]="form.controls.search" [placeholder]="'general.search.title' | transloco">
      </div>
    </div>
  }

  <main>
    <components-table (pageChange)="loadTickets($event)" (rowClicked)="navigateToDetail($event)" [rowClickable]="true" [response]="response" [isLoading]="loading" [amountOfHeaders]="5" class="block">
      <ng-template #headers>
        <th>{{ t('table.number') }}</th>
        <th>{{ t('table.status') }}</th>
        <th>{{ t('table.title') }}</th>
        <th>{{ t('table.created_by') }}</th>
        <th>{{ t('table.updated_at') }}</th>
      </ng-template>
      <ng-template #rows let-row>
        <td>{{ row.id }}</td>
        <td>
          <div
            [ngClass]="{
              'bg-linkmyagency-blue': row.status === status.OPEN,
              'bg-orange-400': row.status === status.IN_PROGRESS || row.status === status.PENDING_CLIENT,
              'bg-green-500': row.status === status.RESOLVED,
              'bg-red-500': row.status === status.REOPENED,
              'bg-gray-400': row.status === status.CLOSED
            }"
            class="btn text-white hover:-translate-y-0 hover:scale-[1.0] w-44 space-x-2"
          >
            @if(row.status === status.OPEN) {
              <i class="fa-regular fa-unlock-keyhole"></i>
            }
            @if(row.status === status.IN_PROGRESS) {
              <i class="fa-solid fa-triangle-exclamation"></i>
            }
            @if(row.status === status.PENDING_CLIENT) {
              <i class="fa-regular fa-clock"></i>
            }
            @if(row.status === status.RESOLVED) {
              <i class="fa-regular fa-circle-check"></i>
            }
            @if(row.status === status.REOPENED) {
              <i class="fa-regular fa-rotate"></i>
            }
            @if(row.status === status.CLOSED) {
              <i class="fa-regular fa-lock-keyhole"></i>
            }
            <span>{{ t('statuses.' + row.status) }}</span>
          </div>
        </td>
        <td>{{ row.title }}</td>
        <td>{{ row.created_by?.first_name }} {{ row.created_by?.last_name }}</td>
        <td>{{ row.updated_at | date }}</td>
      </ng-template>
    </components-table>
  </main>
</content-container>
