import { Component, DestroyRef, OnInit } from '@angular/core';
import { ContentContainerComponent } from '@layout/container/content-container/content-container.component';
import { TableComponent } from '@components/table/table.component';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { Ticket } from '@api/support/models/ticket.interface';
import { TicketService } from '@api/support/services/ticket.service';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  Observable,
  tap,
} from 'rxjs';
import { genericToastError } from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DatePipe, NgClass } from '@angular/common';
import { TicketStatus } from '@api/support/enums/ticket-status.enum';
import { ActivatedRoute, Router } from '@angular/router';
import { AccountService } from '@api/profile/services/account.service';
import { SelectOption } from '@interfaces/select-option.interface';
import { Account } from '@api/profile/models/account.interface';
import { ConnectedSelectComponent } from '@components/form-inputs/connected-select/connected-select.component';
import { SelectComponent } from '@components/form-inputs/select/select.component';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';

interface Form {
  accounts: FormControl<SelectOption<string>[] | null>;
  statuses: FormControl<SelectOption<TicketStatus>[] | null>;
  search: FormControl<string | null>;
}

@Component({
  selector: 'app-index',
  standalone: true,
  imports: [
    ContentContainerComponent,
    TableComponent,
    NgClass,
    TranslocoDirective,
    DatePipe,
    ConnectedSelectComponent,
    SelectComponent,
    TranslocoPipe,
    ReactiveFormsModule,
  ],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public loading: boolean = false;
  public response: PaginatedResponse<Ticket> | undefined;
  public form: FormGroup<Form> | undefined;

  public status = TicketStatus;

  constructor(
    private ticketService: TicketService,
    private translocoService: TranslocoService,
    private toastService: HotToastService,
    private destroyRef: DestroyRef,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private accountService: AccountService,
  ) {}

  public ngOnInit(): void {
    this.loadTickets();
    this.initForm();
  }

  public loadTickets(page: number = 1): void {
    if (this.loading) {
      return;
    }

    this.loading = true;

    this.ticketService
      .index({
        page,
        statuses: this.form?.controls.statuses.value?.map(
          (option) => option.value,
        ),
        account_ids: this.form?.controls.accounts.value?.map(
          (option) => option.value,
        ),
        search: this.form?.controls.search.value,
      })
      .pipe(
        filter((response): response is PaginatedResponse<Ticket> => true),
        tap((response) => {
          this.response = response;
          this.loading = false;
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.loading = false;
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public navigateToDetail(ticket: Ticket): void {
    this.router.navigate([ticket.id], { relativeTo: this.activatedRoute });
  }

  public accountsCallback = (
    page: number,
    filters: { [key: string]: any },
    search: string | null = null,
  ): Observable<PaginatedResponse<SelectOption<string>>> => {
    const mapAccountToSelectOption = (
      account: Account,
    ): SelectOption<string> => ({ value: account.id, label: account.name });

    return this.accountService.index({ page, search }).pipe(
      filter((response): response is PaginatedResponse<Account> => true),
      map(
        (response): PaginatedResponse<SelectOption<string>> => ({
          ...response,
          data: response.data.map((account) =>
            mapAccountToSelectOption(account),
          ),
        }),
      ),
    );
  };

  private initForm(): void {
    this.form = new FormGroup<Form>({
      statuses: new FormControl(null),
      accounts: new FormControl(null),
      search: new FormControl(null),
    });

    this.form.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap(() => {
          this.loadTickets();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
