import { Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { take, tap } from 'rxjs';
import { AttachmentComponent } from '@pages/support/detail/attachment/attachment.component';

@Injectable({
  providedIn: 'root',
})
export class AttachmentService {
  constructor(private modalService: ModalService) {}

  public show(attachment: File | string): void {
    const modal = this.modalService.attach(AttachmentComponent);

    modal.componentRef.instance.attachment = attachment;

    modal.componentRef.instance.close$
      .pipe(
        tap(() => {
          modal.overlayRef.detach();
        }),
        take(1),
      )
      .subscribe();
  }
}
