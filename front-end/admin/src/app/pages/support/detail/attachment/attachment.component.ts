import { Component } from '@angular/core';
import { Subject } from 'rxjs';
import { ModalComponent } from '@components/modal/modal.component';
import { TransformMediaFileToReadableUrlPipe } from '@pipes/transform-media-file-to-readable-url.pipe';

@Component({
  selector: 'app-attachment',
  standalone: true,
  imports: [
    ModalComponent,
    TransformMediaFileToReadableUrlPipe,
    TransformMediaFileToReadableUrlPipe,
  ],
  templateUrl: './attachment.component.html',
})
export class AttachmentComponent {
  public attachment: File | string | undefined;
  public close$: Subject<void> = new Subject();
  public isImage: boolean = true;
  public isFile: boolean = false;

  public ngOnInit(): void {
    if (!this.attachment) {
      return;
    }

    if (this.attachment instanceof File) {
      this.isImage = this.attachment.type.startsWith('image/');
      this.isFile = true;
      return;
    }

    const urlWithoutParams = this.attachment.split('?')[0];
    this.isImage = !!urlWithoutParams.match(/\.(jpg|jpeg|png|gif)$/i);
  }

  public close(): void {
    this.close$.next();
    this.close$.complete();
  }
}
