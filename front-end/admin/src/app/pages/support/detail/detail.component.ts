import { Component, DestroyRef, Input } from '@angular/core';
import { ContentContainerComponent } from '@layout/container/content-container/content-container.component';
import { catchError, tap } from 'rxjs';
import {
  genericToastError,
  genericToastSuccess,
} from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Ticket } from '@api/support/models/ticket.interface';
import { TicketStatus } from '@api/support/enums/ticket-status.enum';
import { TicketService } from '@api/support/services/ticket.service';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import { AttachmentService } from '@pages/support/detail/attachment/attachment.service';
import { RouterLink } from '@angular/router';
import { TransformMediaFileToReadableUrlPipe } from '@pipes/transform-media-file-to-readable-url.pipe';
import { MessagesComponent } from '@pages/support/detail/messages/messages.component';
import { UrlIsImagePipe } from '@pipes/url-is-image.pipe';
import { KeyValuePipe } from '@angular/common';

interface Form {
  title: FormControl<string | null>;
  description: FormControl<string | null>;
  status: FormControl<TicketStatus>;
}

@Component({
  selector: 'app-detail',
  standalone: true,
  imports: [
    ContentContainerComponent,
    ReactiveFormsModule,
    RouterLink,
    TranslocoPipe,
    TransformMediaFileToReadableUrlPipe,
    MessagesComponent,
    UrlIsImagePipe,
    TranslocoDirective,
    KeyValuePipe,
  ],
  templateUrl: './detail.component.html',
})
export class DetailComponent {
  @Input({ alias: 'id' }) ticketId: string | undefined;
  public form: FormGroup<Form> | undefined;
  public storing: boolean = false;
  public ticket: Ticket | undefined;
  public submitted: boolean = false;
  public attachments: File[] = [];
  public loading: boolean = false;

  public status = TicketStatus;

  constructor(
    private ticketService: TicketService,
    private translocoService: TranslocoService,
    private toastService: HotToastService,
    private destroyRef: DestroyRef,
    private attachmentService: AttachmentService,
  ) {}

  public ngOnInit(): void {
    this.loadTicket();
  }

  public openAttachment(attachment: File | string): void {
    this.attachmentService.show(attachment);
  }

  public save(): void {
    if (this.storing || !this.form || this.form.invalid || !this.ticket) {
      return;
    }

    this.storing = true;

    this.ticketService
      .status(this.ticket, {
        status: this.form.controls.status.value,
      })
      .pipe(
        tap((response) => {
          this.ticket = response.data;
          this.storing = false;
          genericToastSuccess(this.toastService, this.translocoService);
        }),
        catchError((err) => {
          this.storing = false;
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadTicket(): void {
    if (!this.ticketId) {
      this.initForm();
      return;
    }

    this.loading = true;

    this.ticketService
      .show(this.ticketId)
      .pipe(
        tap((response) => {
          this.ticket = response.data;
          this.initForm();
          this.loading = false;
        }),
        catchError((err) => {
          this.initForm();
          this.loading = false;
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initForm(): void {
    if (!this.ticket) {
      return;
    }

    this.form = new FormGroup<Form>({
      title: new FormControl({ value: this.ticket.title, disabled: true }),
      description: new FormControl({
        value: this.ticket.description,
        disabled: true,
      }),
      status: new FormControl(
        {
          value: this.ticket.status,
          disabled: this.ticket.status === TicketStatus.CLOSED,
        },
        {
          validators: [Validators.required],
          nonNullable: true,
        },
      ),
    });
  }
}
