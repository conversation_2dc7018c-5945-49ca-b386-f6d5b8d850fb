<div *transloco="let t; read: 'pages.support.detail'" class="space-y-4">
  @if(!loading) {
    @if(data && (data|keyvalue).length > 0) {
      <div scrollToBottom [scrollToBottomTrigger]="data" class="max-h-[40vh] overflow-y-auto border bg-gray-100 border-gray-100 p-2 rounded space-y-4">
        @for(date of data | keyvalue; track $index) {
          <div class="flex justify-center">
            <p>{{ date.key | date }}</p>
          </div>
          @for(message of date.value.messages; track $index) {
            <div
              [ngClass]="{
                'ml-auto mr-0': message.created_by?.account_id !== ticket?.account_id
              }"
              class="w-fit max-w-[33%]"
            >
              <div
                [ngClass]="{
                  'mr-0 ml-auto': message.created_by?.account_id !== ticket?.account_id
                }"
                class="w-fit"
              >
                <div
                  [ngClass]="{
                    'bg-linkmyagency-blue/70 rounded-bl-none': message.created_by?.account_id === ticket?.account_id,
                    'rounded-br-none': message.created_by?.account_id !== ticket?.account_id,
                  }"
                  class="bg-linkmyagency-blue text-white w-full px-3 py-1.5 rounded relative rounded-br-none min-w-36">
                  <p>{{ message.message }}</p>

                  <p class="text-xs">{{ message.created_at | date: 'HH:mm' }}</p>
                </div>
                <p
                  [ngClass]="{
                    'pr-1': message.created_by?.account_id !== ticket?.account_id,
                    'pl-1': message.created_by?.account_id === ticket?.account_id,
                  }"
                  class="text-xs">
                  @if(message.created_by) {
                    {{ message.created_by?.first_name }} {{ message.created_by?.last_name }}
                  } @else {
                    {{ t('system') }}
                  }
                </p>
              </div>

              <div class="flex space-x-4 overflow-x-auto p-1 w-full">
                @for (attachment of message?.attachments ?? []; track index; let index = $index) {
                  <div (click)="openAttachment(attachment.url)" class="h-16 w-24 min-w-24 p-1 rounded shadow hover:-translate-y-[2px] hover:scale-[1.02] transition cursor-pointer relative bg-white">
                    @if(attachment.url | urlIsImage) {
                      <div [style.background-image]="'url(' + attachment.url + ')'" class="h-full w-full bg-no-repeat bg-center bg-contain"></div>
                    } @else {
                      <video [src]="attachment.url" class="h-full w-full"></video>
                    }
                  </div>
                }
              </div>
            </div>
          }
        }
      </div>
    }

    @if(form) {
      <div class="space-y-2">
        <div class="form-group-new">
          <label>{{ t('message') }}</label>
          <textarea [formControl]="form.controls.message" class="w-full" rows="5"></textarea>
        </div>

        <div class="flex justify-between items-center">
          <h2 class="font-medium">{{ t('attachments', {count: attachments.length}) }}</h2>
          <label for="upload-attachments-message" class="btn !p-0 cursor-pointer hover:shadow-none">
            <i class="fa-regular fa-plus text-lg"></i>
          </label>
          <input (change)="attachmentsUploaded($event)" multiple id="upload-attachments-message" type="file" accept="image/jpeg,image/png,image/gif,video/mp4,video/mpeg,video/quicktime" class="hidden">
        </div>

        <div class="flex space-x-4 overflow-x-auto p-1 w-full">
          @for (attachment of attachments; track index; let index = $index) {
            @if({url: attachment | transformMediaFileToReadableUrl}; as data) {
              <div (click)="openAttachment(attachment)" class="h-16 w-24 min-w-24 p-1 rounded shadow hover:-translate-y-[2px] hover:scale-[1.02] transition cursor-pointer relative">
                @if(attachment.type.startsWith('image/')) {
                  <div [style.background-image]="'url(' + data.url + ')'" class="h-full w-full bg-no-repeat bg-center bg-contain"></div>
                } @else {
                  <video [src]="data.url" class="h-full w-full"></video>
                }
                <div (click)="removeAttachment(index); $event.stopPropagation()" class="absolute h-6 w-6 bg-gray-300/40 flex items-center justify-center top-1 right-1 rounded-full">
                  <i class="fa-regular fa-xmark"></i>
                </div>
              </div>
            }
          }
        </div>

        <div class="flex justify-end">
          <button (click)="submit()" [class.--disabled]="form.invalid" [class.loading]="sending" class="btn --blue !p-0 h-8 w-8 !rounded-full flex items-center justify-center" >
            <i class="fa-solid fa-paper-plane-top"></i>
          </button>
        </div>
      </div>
    }
  }
</div>
