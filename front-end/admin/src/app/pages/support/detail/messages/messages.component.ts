import { Component, DestroyRef, Input } from '@angular/core';
import { Ticket } from '@api/support/models/ticket.interface';
import { MessageService } from '@api/support/services/message.service';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Message } from '@api/support/models/message.interface';
import { DatePipe, formatDate, KeyValuePipe, NgClass } from '@angular/common';
import { ScrollToBottomDirective } from '@directives/scroll-to-bottom.directive';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TransformMediaFileToReadableUrlPipe } from '@pipes/transform-media-file-to-readable-url.pipe';
import { genericToastError } from '@helpers/generic-toasts';
import { UrlIsImagePipe } from '@pipes/url-is-image.pipe';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import { AttachmentService } from '@pages/support/detail/attachment/attachment.service';
import { DateCollection } from '@api/support/responses/date-collection.interface';

interface Form {
  message: FormControl<string | null>;
}

@Component({
  selector: 'support-detail-messages',
  standalone: true,
  imports: [
    DatePipe,
    ScrollToBottomDirective,
    TranslocoDirective,
    TransformMediaFileToReadableUrlPipe,
    ReactiveFormsModule,
    UrlIsImagePipe,
    NgClass,
    KeyValuePipe,
  ],
  templateUrl: './messages.component.html',
})
export class MessagesComponent {
  @Input() ticket: Ticket | undefined;
  public loading: boolean = false;
  public form: FormGroup<Form> | undefined;
  public attachments: File[] = [];
  public sending: boolean = false;
  public data: DateCollection | undefined;

  constructor(
    private messageService: MessageService,
    private translocoService: TranslocoService,
    private toastService: HotToastService,
    private destroyRef: DestroyRef,
    private attachmentService: AttachmentService,
  ) {}

  public ngOnInit(): void {
    this.loadMessages();
    this.initForm();
  }

  public attachmentsUploaded(event: any): void {
    const files = event.target?.files as File[];

    if (files.length === 0) {
      return;
    }

    this.attachments.push(...files);
  }

  public openAttachment(attachment: File | string): void {
    this.attachmentService.show(attachment);
  }

  public removeAttachment(index: number): void {
    this.attachments.splice(index, 1);
  }

  public submit(): void {
    if (
      this.loading ||
      this.sending ||
      !this.form ||
      this.form.invalid ||
      !this.ticket
    ) {
      this.form?.markAllAsTouched();
      return;
    }

    const currentDate = formatDate(new Date(), 'yyyy-MM-dd', 'en-US');

    this.sending = true;

    this.messageService
      .store(this.ticket, {
        message: this.form.controls.message.value as string,
        attachments:
          this.attachments.length > 0
            ? this.attachments.map((file) => ({ file }))
            : null,
      })
      .pipe(
        tap((response) => {
          if (this.data && currentDate in this.data) {
            this.data[currentDate].messages.push(response.data);
          } else if (this.data) {
            this.data[currentDate] = { messages: [response.data] };
          } else {
            this.data = {
              currentDate: { messages: [response.data] },
            };
          }

          this.sending = false;
          this.form?.controls.message.setValue(null);
          this.attachments = [];
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.sending = false;
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadMessages(): void {
    if (this.loading || !this.ticket) {
      return;
    }

    this.loading = true;

    this.messageService
      .index(this.ticket)
      .pipe(
        tap((response) => {
          this.data = response.data;
          this.loading = false;
        }),
        catchError((err) => {
          this.loading = false;
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initForm(): void {
    this.form = new FormGroup<Form>({
      message: new FormControl(null, [Validators.required]),
    });
  }
}
