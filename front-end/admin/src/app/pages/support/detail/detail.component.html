<content-container *transloco="let t; read: 'pages.support.detail'">
  <div class="space-y-4">
    <div>
      <button routerLink="/support" class="link space-x-2">
        <i class="fa-regular fa-arrow-left"></i>
        <span>{{ 'general.buttons.back' | transloco }}</span>
      </button>
    </div>

    @if(form && !loading) {
      <div [class.ng-submitted]="submitted" class="shadow-sm ring-1 ring-gray-900/5 p-6 rounded-xl bg-white space-y-2">
        <div class="form-group-new">
          <label>{{ t('title') }}</label>
          <input [formControl]="form.controls.title">
        </div>

        <div class="form-group-new">
          <label>{{ t('description') }}</label>
          <textarea [formControl]="form.controls.description" rows="5"></textarea>
        </div>

        <div class="form-group-new">
          <label>{{ t('status') }}</label>
          <select [formControl]="form.controls.status">
            <option [ngValue]="status.OPEN">{{ 'pages.support.index.statuses.OPEN' | transloco }}</option>
            <option [ngValue]="status.IN_PROGRESS">{{ 'pages.support.index.statuses.IN_PROGRESS' | transloco }}</option>
            <option [ngValue]="status.PENDING_CLIENT">{{ 'pages.support.index.statuses.PENDING_CLIENT' | transloco }}</option>
            <option [ngValue]="status.RESOLVED">{{ 'pages.support.index.statuses.RESOLVED' | transloco }}</option>
            <option [ngValue]="status.REOPENED">{{ 'pages.support.index.statuses.REOPENED' | transloco }}</option>
            <option [ngValue]="status.CLOSED" disabled>{{ 'pages.support.index.statuses.CLOSED' | transloco }}</option>
          </select>
        </div>

        <div class="flex justify-between items-center">
          <h2 class="font-medium">{{ t('attachments', {count: ticket ? (ticket.attachments?.length ?? 0) : attachments.length}) }}</h2>
        </div>

        <div class="flex space-x-4 overflow-x-auto p-1 w-full">
          @for (attachment of ticket?.attachments ?? []; track index; let index = $index) {
            <div (click)="openAttachment(attachment.url)" class="h-16 w-24 min-w-24 p-1 rounded shadow hover:-translate-y-[2px] hover:scale-[1.02] transition cursor-pointer relative">
              @if(attachment.url | urlIsImage) {
                <div [style.background-image]="'url(' + attachment.url + ')'" class="h-full w-full bg-no-repeat bg-center bg-contain"></div>
              } @else {
                <video [src]="attachment.url" class="h-full w-full"></video>
              }
            </div>
          }
        </div>

        @if (form.dirty) {
          <div class="flex justify-end">
            <button (click)="save()" [class.loading]="storing" class="btn --blue">{{ 'general.buttons.save' | transloco }}</button>
          </div>
        }
      </div>

      @if(ticket) {
        <div class="shadow-sm ring-1 ring-gray-900/5 p-6 rounded-xl space-y-6 bg-white">
          <support-detail-messages [ticket]="ticket"></support-detail-messages>
        </div>
      }
    }

    @if (loading) {
      <div class="flex justify-center items-center h-96">
        <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-2xl"></i>
      </div>
    }
  </div>
</content-container>
