@if (form) {
  <div *transloco="let t; read: 'pages.auth.login'">
    <h2 class="text-3xl font-bold text-center text-eaglo-gray mb-8">{{ t('title') }}</h2>
    <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-6">
      <div class="form-group">
        <label class="block text-sm font-medium text-eaglo-gray mb-1">{{ t('email') }}</label>
        <input [formControl]="form.controls.email" type="email" autocomplete="email">
      </div>
      <div class="form-group">
        <label class="block text-sm font-medium text-eaglo-gray mb-1">{{ t('password') }}</label>
        <input [formControl]="form.controls.password" type="password" autocomplete="current-password">
      </div>
      <button [class.--loading]="loading" type="submit" class="btn w-full">
        {{ 'general.buttons.login' | transloco }}
      </button>
    </form>
  </div>
}
