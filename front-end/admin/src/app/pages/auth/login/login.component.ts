import { Component, DestroyRef, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import {
  TranslocoPipe,
  TranslocoDirective,
  TranslocoService,
} from '@jsverse/transloco';
import { AuthenticationService } from '@api/auth/services/authentication.service';
import { LoginRequest } from '@api/auth/requests/login.request';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LOGGED_IN_LOCAL_STORAGE_KEY } from '@app/services/auth.service';
import { ToastService } from '@services/toast.service';

interface LoginForm {
  email: FormControl<string | null>;
  password: FormControl<string | null>;
}

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterLink,
    TranslocoPipe,
    TranslocoDirective,
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  public form!: FormGroup<LoginForm>;
  public loading: boolean = false;

  constructor(
    private authenticationService: AuthenticationService,
    private destroyRef: DestroyRef,
    private router: Router,
    private translocoService: TranslocoService,
    private toastService: ToastService,
  ) {}

  public ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    this.form = new FormGroup<LoginForm>({
      email: new FormControl<string | null>(null, [
        Validators.required,
        Validators.email,
      ]),
      password: new FormControl<string | null>(null, [Validators.required]),
    });
  }

  public onSubmit(): void {
    if (!this.form || this.form.invalid) {
      this.form?.markAllAsTouched();
      return;
    }

    this.loading = true;

    this.authenticationService
      .login(this.form.value as LoginRequest)
      .pipe(
        tap((response) => {
          this.loading = false;
          localStorage.setItem(LOGGED_IN_LOCAL_STORAGE_KEY, 'true');
          this.router.navigate(['/']);
        }),
        catchError((err) => {
          let title = null;
          let description = null;

          if (err.status === 401 || err.status === 403) {
            title = 'pages.auth.login.toasts.error.title';
            description = 'pages.auth.login.toasts.error.' + err.status;
          }

          this.toastService.error(title, description);

          this.loading = false;
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
