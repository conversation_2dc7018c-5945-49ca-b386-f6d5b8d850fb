import { Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, tap } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { AuthenticationService } from '@api/auth/services/authentication.service';
import { Admin } from '@api/auth/models/admin.model';

export const LOGGED_IN_LOCAL_STORAGE_KEY = 'logged_in';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  public user = signal<Admin | null>(null);
  private impersonating: boolean = false;

  constructor(
    private router: Router,
    private authenticationService: AuthenticationService,
  ) {}

  public isLoggedIn(): boolean {
    return !!localStorage.getItem(LOGGED_IN_LOCAL_STORAGE_KEY);
  }

  public logout(): void {
    this.user.set(null);

    this.authenticationService.logout().subscribe();
    localStorage.removeItem(LOGGED_IN_LOCAL_STORAGE_KEY);

    this.router.navigate(['/auth']);
  }

  public loadUser(): Observable<DataResponse<Admin>> {
    return this.authenticationService.me().pipe(
      tap((response) => {
        this.user.set(response.data);
      }),
    );
  }

  public isImpersonating(): boolean {
    return this.impersonating;
  }

  public setImpersonating(value: boolean): void {
    this.impersonating = value;
  }
}
