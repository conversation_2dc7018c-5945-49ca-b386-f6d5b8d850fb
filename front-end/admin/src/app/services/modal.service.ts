import { ComponentRef, Injectable } from '@angular/core';
import {
  ComponentType,
  GlobalPositionStrategy,
  Overlay,
  OverlayConfig,
  OverlayRef,
} from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  constructor(private overlay: Overlay) {}

  public attach<T>(component: ComponentType<T>): {
    overlayRef: OverlayRef;
    componentRef: ComponentRef<T>;
  } {
    const overlayRef = this.overlay.create(ModalService.getOverlayConfig());
    const componentPortal = new ComponentPortal(component);

    const componentRef = overlayRef.attach(componentPortal);

    return {
      overlayRef,
      componentRef,
    };
  }

  private static getPositionStrategy(): GlobalPositionStrategy {
    const strategy = new GlobalPositionStrategy();

    strategy.centerHorizontally();
    strategy.centerVertically();

    return strategy;
  }

  private static getOverlayConfig(): OverlayConfig {
    return new OverlayConfig({
      hasBackdrop: true,
      positionStrategy: ModalService.getPositionStrategy(),
    });
  }
}
