import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  forwardRef,
  HostListener,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NgClass } from '@angular/common';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { SelectOption } from '@app/interfaces/select-option.interface';

@Component({
  selector: 'form-select',
  standalone: true,
  imports: [TranslocoDirective, ReactiveFormsModule, NgClass, TranslocoPipe],
  templateUrl: './select.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SelectComponent),
      multi: true,
    },
  ],
})
export class SelectComponent
  implements ControlValueAccessor, OnInit, OnChanges
{
  // Inputs
  @Input() data: SelectOption<any>[] = [];
  @Input() multiple = false;
  @Input() isLoading = false;
  @Input() isInvalid = false;
  @Input() label?: string;

  // Outputs
  @Output() nextPage: EventEmitter<void> = new EventEmitter<void>();
  @Output() newSearchValue: EventEmitter<string | null> = new EventEmitter<
    string | null
  >();

  // ViewChild
  @ViewChild('container') container!: ElementRef;
  @ViewChild('scrollContainer') scrollContainer?: ElementRef;
  @ViewChild('searchValueControl') searchValueControl?: ElementRef;

  // State
  public scrollListenerIsActive = true;
  public selectOpened = false;
  public selectedValues: SelectOption<any>[] = [];
  public searchOpened = false;
  public searchValue: FormControl<string | null> = new FormControl();

  // Inject
  public destroyRef = inject(DestroyRef);
  protected changeDetectorRef = inject(ChangeDetectorRef);

  // Change functions for ControlValueAccessor
  public onChange: any = () => {};
  public onTouched: any = () => {};

  // Register change function for ControlValueAccessor
  public registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  // Register touched function for ControlValueAccessor
  public registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  // External writes function for ControlValueAccessor
  public writeValue(obj: SelectOption<any> | SelectOption<any>[] | null): void {
    this.selectOpened = false;

    if (!obj) {
      this.selectedValues = [];
      return;
    }

    if (!this.data) {
      this.selectedValues = Array.isArray(obj) ? obj : [obj];
      return;
    }

    this.setSelectedValuesFromData(obj);
  }

  public ngOnInit(): void {
    this.listenToSearch();
  }

  public ngOnChanges(changes: SimpleChanges) {
    if (changes['isLoading'] && changes['isLoading'].firstChange) {
      this.activateScrollListener();
    }
  }

  public toggleSelect(): void {
    if (this.searchOpened) {
      return;
    }

    this.selectOpened = !this.selectOpened;
    this.clearSearchValue();
    this.changeDetectorRef.detectChanges();
  }

  public toggleSearch(): void {
    if (!this.selectOpened) {
      return;
    }

    this.searchOpened = !this.searchOpened;

    if (!this.searchOpened) {
      this.searchValue.setValue(null);
    }

    this.changeDetectorRef.detectChanges();

    if (this.searchOpened) {
      setTimeout(() => {
        this.searchValueControl?.nativeElement.focus();
        this.changeDetectorRef.detectChanges();
      }, 50);
    }
  }

  public containsValue(item: SelectOption<any>): boolean {
    return (
      this.selectedValues.filter((value) => value.value === item.value).length >
      0
    );
  }

  public clearSearchValue(): void {
    if (this.selectOpened) {
      return;
    }

    this.searchOpened = false;
    this.searchValue.setValue(null);

    this.changeDetectorRef.detectChanges();
  }

  public selectValue(item: SelectOption<any>): void {
    if (item.disabled) {
      return;
    }

    this.selectOpened = false;

    if (!this.multiple) {
      this.selectedValues = [item];
    }

    if (this.multiple) {
      if (this.containsValue(item)) {
        this.selectedValues = this.selectedValues.filter(
          (value) => value.value !== item.value,
        );
      } else {
        this.selectedValues.push(item);
      }
    }

    this.onChange(this.multiple ? this.selectedValues : this.selectedValues[0]);
    this.onTouched(
      this.multiple ? this.selectedValues : this.selectedValues[0],
    );

    this.changeDetectorRef.detectChanges();
  }

  public activateScrollListener(): void {
    setTimeout(() => {
      this.scrollListenerIsActive = true;
    }, 200);
  }

  public listenToSearch(): void {
    this.searchValue.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((value) => {
        this.newSearchValue.emit(value);
      });
  }

  @HostListener('document:click', ['$event'])
  public onClick(event: MouseEvent) {
    const container = this.container.nativeElement;
    const scrollContainer = this.scrollContainer?.nativeElement;
    const clickedOnContainer = container.contains(event.target);
    const clickedOnScrollContainer = scrollContainer?.contains(event.target);

    if (!clickedOnContainer && !clickedOnScrollContainer) {
      this.selectOpened = false;
      this.clearSearchValue();
    }

    this.changeDetectorRef.detectChanges();
  }

  public onScroll(event: Event): void {
    const element = event.target as HTMLElement;
    const differenceToBottom = element.scrollHeight - element.scrollTop;
    const allowSafetyMargin = 25;

    if (
      !this.isLoading &&
      this.scrollListenerIsActive &&
      differenceToBottom < element.clientHeight + allowSafetyMargin
    ) {
      this.isLoading = true;
      this.scrollListenerIsActive = false;

      this.nextPage.emit();
    }
  }

  public setSelectedValuesFromData(
    obj: SelectOption<any> | SelectOption<any>[],
  ): void {
    const getFilteredValueFromData = (
      option: SelectOption<any>,
    ): SelectOption<any> => {
      const filtered = this.data.filter(
        (entry) => entry.value === option.value,
      );

      if (filtered.length === 0) {
        return option;
      }

      return filtered[0];
    };

    if (Array.isArray(obj)) {
      this.selectedValues = obj.map((option) =>
        getFilteredValueFromData(option),
      );
      return;
    }

    this.selectedValues = [getFilteredValueFromData(obj)];
  }

  public selectOrDeselectAll(): void {
    if (!this.multiple) {
      return;
    }

    if (this.selectedValues.length !== this.data.length) {
      this.selectedValues = this.data;
    } else {
      this.selectedValues = [];
    }

    this.onChange(this.selectedValues);
    this.onTouched(this.selectedValues);
    this.selectOpened = false;
  }
}
