@if(response) {
  <nav *transloco="let t; read: 'components.pagination'" class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded" aria-label="Pagination">
    <div class="hidden sm:block">
      <p class="text-sm text-gray-700">
        {{ t('showing') }}
        <span class="font-medium">{{ response.meta.from }}</span>
        {{ t('to') }}
        <span class="font-medium">{{ response.meta.to }}</span>
        {{ t('of') }}
        <span class="font-medium">{{ response.meta.total }}</span>
        {{ t('results') }}
      </p>
    </div>
    <div class="flex flex-1 justify-between sm:justify-end">
      @if(response.meta.current_page > 1) {
        <button (click)="pageChange(response.meta.current_page - 1)" class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">{{ t('previous') }}</button>
      }
      @if(response.meta.current_page < response.meta.last_page) {
        <button (click)="pageChange(response.meta.current_page + 1)" class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">{{ t('next') }}</button>
      }
    </div>
  </nav>
}