import { NgClass } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  HostListener,
  Input,
  ViewChild,
} from '@angular/core';

@Component({
  selector: 'components-dropdown',
  imports: [NgClass],
  templateUrl: './dropdown.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DropdownComponent {
  @Input() width: string = 'w-40';
  @Input() customIcon: boolean = false;
  @Input() inverted: boolean = false;
  @Input() hover: boolean = false;
  @Input() disabled: boolean = false;
  @Input() needsFlex: boolean = true;
  @ViewChild('container') container!: ElementRef;
  @ViewChild('icon') icon?: ElementRef;
  public open: boolean = false;

  public toggleOpen(isHover: boolean = false): void {
    if (this.disabled) {
      return;
    }

    if (isHover && !this.hover) {
      return;
    }
    this.open = !this.open;

    const rect = this.icon?.nativeElement.getBoundingClientRect();

    if (this.container?.nativeElement && rect) {
      this.container.nativeElement.style.top = `${rect.top + rect.height + 4}px`;
      if (this.inverted) {
        this.container.nativeElement.style.left = `${rect.left}px`;
      } else {
        this.container.nativeElement.style.left = `${rect.left - this.container.nativeElement.getBoundingClientRect().width}px`;
      }
    }
  }

  @HostListener('document:click', ['$event'])
  public onClick(event: MouseEvent) {
    const container = this.container.nativeElement;
    const clickedOnContainer = container.contains(event.target);

    if (!clickedOnContainer) {
      this.open = false;
    }
  }

  @HostListener('window:scroll', ['$event'])
  public windowScroll(event: MouseEvent) {
    this.open = false;
  }
}
