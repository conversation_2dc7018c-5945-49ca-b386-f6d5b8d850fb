import { Injectable, inject } from '@angular/core';
import {
  HttpRequest,
  HttpHandlerFn,
  HttpEvent,
  HttpInterceptorFn,
  HttpErrorResponse,
} from '@angular/common/http';
import { catchError, Observable, throwError } from 'rxjs';
import { AuthService } from '@services/auth.service';
import { ConfigService } from '@services/config.service';

const ignoredEndpoints: string[] = [
  '/api/v1/admin/auth/logout',
  '/api/v1/admin/auth/login',
];

export const unauthenticatedInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
  const configService = inject(ConfigService);
  const authService = inject(AuthService);

  const relativeRequestUrl = request.url.replace(
    configService.config?.environment.endpoint ?? '',
    '',
  );

  if (hasMatchingEndpoint(relativeRequestUrl)) {
    return next(request);
  }

  return next(request).pipe(
    catchError((err) => {
      if (isUnauthenticatedResponse(err)) {
        authService.logout();
      }
      return throwError(() => err);
    }),
  );
};

function isUnauthenticatedResponse(err: any): boolean {
  const isHttpErrorResponse = err instanceof HttpErrorResponse;
  const isUnauthenticatedStatus = err.status === 401;

  return isHttpErrorResponse && isUnauthenticatedStatus;
}

function hasMatchingEndpoint(relativeRequestUrl: string): boolean {
  for (const endpoint of ignoredEndpoints) {
    if (relativeRequestUrl.startsWith(endpoint)) {
      return true;
    }
  }
  return false;
}
