import { CanActivateFn } from '@angular/router';
import { inject } from '@angular/core';
import {
  AuthService,
  LOGGED_IN_LOCAL_STORAGE_KEY,
} from '@services/auth.service';
import { CookieService } from 'ngx-cookie-service';

export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const cookieService = inject(CookieService);

  if (cookieService.get('impersonating')) {
    localStorage.setItem(LOGGED_IN_LOCAL_STORAGE_KEY, 'true');
    authService.setImpersonating(true);
    return true;
  }

  if (authService.isLoggedIn()) {
    return true;
  }

  authService.logout();

  return false;
};
