import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  HostListener,
  inject,
  signal,
  ViewChild,
} from '@angular/core';
import { RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { AuthService } from '@app/services/auth.service';
import { TranslocoDirective } from '@jsverse/transloco';

@Component({
  selector: 'app-container',
  imports: [RouterOutlet, RouterLink, RouterLinkActive, TranslocoDirective],
  templateUrl: './container.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrl: './container.component.scss',
})
export class ContainerComponent {
  public readonly dropdownOpen = signal(false);
  @ViewChild('container') container!: ElementRef;

  private authService = inject(AuthService);

  public toggleDropdown(): void {
    this.dropdownOpen.update((open) => !open);
  }

  public isDropdownOpen(): boolean {
    return this.dropdownOpen();
  }

  public logout(): void {
    this.authService.logout();
  }

  @HostListener('document:click', ['$event'])
  public onClick(event: MouseEvent) {
    const container = this.container?.nativeElement;

    if (!container) {
      return;
    }

    const clickedOnContainer = container.contains(event.target);

    if (!clickedOnContainer) {
      this.toggleDropdown();
    }
  }
}
