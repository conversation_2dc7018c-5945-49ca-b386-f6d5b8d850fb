import { Routes } from '@angular/router';
import { authGuard } from '@guards/auth.guard';
import { authenticatedUserResolver } from './resolvers/authenticatedUserResolver';
import { ContainerComponent } from './layouts/container/container.component';

export const routes: Routes = [
  {
    path: '',
    canActivate: [authGuard],
    resolve: {
      me: authenticatedUserResolver,
    },
    component: ContainerComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'accounts',
      },
      {
        path: 'accounts',
        loadChildren: () =>
          import('./pages/accounts/accounts.routes').then((r) => r.routes),
      },
      {
        path: 'emails',
        loadChildren: () =>
          import('@pages/emails/emails.routes').then((r) => r.routes),
      },
      // {
      //   path: 'support',
      //   loadChildren: () =>
      //     import('@pages/support/support-routes').then((r) => r.routes),
      // },
    ],
  },
  {
    path: 'auth',
    loadChildren: () =>
      import('./pages/auth/auth.routes').then((m) => m.AUTH_ROUTES),
  },
];
