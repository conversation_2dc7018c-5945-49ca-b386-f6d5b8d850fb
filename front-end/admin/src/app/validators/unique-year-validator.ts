import {
  AbstractControl,
  ValidationErrors,
  ValidatorFn,
  FormArray,
} from '@angular/forms';
import { getYear } from 'date-fns';

export function uniqueYearValidator(): ValidatorFn {
  return (formArray: AbstractControl): ValidationErrors | null => {
    if (!(formArray instanceof FormArray)) {
      return null;
    }

    const years = formArray.controls
      .map((control) => {
        const yearDate = control.get('year')?.value;
        return yearDate ? getYear(yearDate) : null;
      })
      .filter((year) => year !== null && year !== undefined);

    const duplicateYears = years.filter(
      (year, index) => years.indexOf(year) !== index,
    );

    if (duplicateYears.length > 0) {
      // Mark the duplicate controls as invalid
      formArray.controls.forEach((control) => {
        const yearControl = control.get('year');
        const yearDate = yearControl?.value;

        if (yearDate && duplicateYears.includes(getYear(yearDate))) {
          yearControl.setErrors({ duplicateYear: true });
        }
      });

      return { duplicateYears: true };
    }

    // Clear duplicate errors if no duplicates found
    formArray.controls.forEach((control) => {
      const yearControl = control.get('year');
      if (yearControl && yearControl.errors?.['duplicateYear']) {
        delete yearControl.errors['duplicateYear'];
        if (Object.keys(yearControl.errors).length === 0) {
          yearControl.setErrors(null);
        }
      }
    });

    return null;
  };
}
