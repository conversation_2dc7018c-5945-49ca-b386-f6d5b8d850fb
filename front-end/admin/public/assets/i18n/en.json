{"general": {"search": "Search...", "select": "Select", "buttons": {"save": "Save", "confirm": "Confirm", "cancel": "Cancel", "delete": "Delete", "create": "Create", "update": "Update", "complete": "Complete", "review": "Review", "reset": "Reset", "add": "Add", "overview": "Overview", "accept": "Accept", "reject": "Reject", "edit": "Edit", "done": "Done", "preview": "Preview", "share": "Share", "back_to_portal": "Back to portal", "next": "Next", "previous": "Previous", "mark_complete": "<PERSON><PERSON>", "close": "Close", "change_selection": "Change selection", "mark_as_complete": "Mark as complete", "remove": "Remove", "retry": "Retry", "refresh": "Refresh", "send": "Send", "login": "<PERSON><PERSON>", "register": "Register", "select_all": "Select all", "deselect_all": "Deselect all", "open": "Open", "submit": "Submit"}, "selected_all": "All selected", "unknown": "Unknown", "back": "Back", "toasts": {"error": {"title": "Oops, something went wrong!", "description": "It looks like something went wrong on our end. Please try again or contact our support."}, "success": {"title": "Success!", "description": "The action you performed has been successfully completed."}}, "bool": {"true": "Yes", "false": "No"}, "manage": "Manage", "empty_data": "It looks like there is no data available.", "from": "From", "until": "Until", "to": "To", "filter": "Filter", "sort": "Sort by", "settings": "Settings", "request_support": "Request support", "date": "Date", "google": "Google", "microsoft": "Microsoft", "facebook": "Facebook", "linkedin": "LinkedIn", "pinterest": "Pinterest", "tiktok": "TikTok", "instagram": "Instagram", "connected": "Connected", "error": "Error", "preview_mode": "Preview mode. Open in incognito to exit preview mode.", "skipped": "Skipped", "skip": "<PERSON><PERSON>", "synced": "Synced", "connect": "Connect", "failed": "Failed", "failed_data": "Successful", "success": "Success", "pending": "Pending", "connect_your_accounts": "Connect your accounts", "add_another": "Add another one", "skipped_drawer": {"title": "Connection skipped!", "description": "It looks like you have skipped this connection."}, "syncing": "Syncing", "empty_placeholder": "There are no entry's found"}, "pages": {"auth": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "email_required": "Email is required", "invalid_email": "Please enter a valid email address", "password": "Password", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters", "no_account": "Don't have an account?", "register": "Register now"}, "register": {"title": "Register", "name": "Company Name", "name_required": "Company name is required", "firstname": "First Name", "firstname_required": "First name is required", "lastname": "Last Name", "lastname_required": "Last name is required", "email": "Email", "email_required": "Email is required", "invalid_email": "Please enter a valid email address", "password": "Password", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters", "already_account": "Already have an account?", "login": "Login now"}}, "accounts": {"index": {"title": "Accounts", "invite": "Invite", "table": {"name": "Name", "product": "Product", "status": "Status", "last_login_at": "Last login at"}, "buttons": {"stripe": "Stripe", "activate": "Activate", "block": "Block", "tapfiliate": "Tapfiliate"}, "delete": {"title": "Delete <<name>>?", "description": "Are you sure you want to delete <<name>>? This action is irreversible and all data for this account will be deleted and cannot be recovered."}, "invite-modal": {"title": "Send invite", "description": "Send invite to a person to join Eaglo.", "firstname": "Firstname", "lastname": "Lastname", "email": "Email"}}, "toggle-verification": {"title": {"activate": "Activate <<name>>", "block": "Block <<name>>"}, "description": {"activate": "Do you want to activate this account?", "block": "Are you sure you want to block this account? Once blocked they will not be able to login."}}, "detail": {"nav": {"general": "General", "invoices": "Invoices", "users": "Users", "features": "Features"}, "buttons": {"activate": "Activate", "block": "Block"}, "general": {"stripe_id": "Stripe id", "tapfiliate_id": "Tapfiliate id", "tapfiliate_ref": "Tapfiliate reference", "verified_at": "Verified at", "billing_email": "Billing email", "address": "address"}, "users": {"index": {"name": "Name", "email": "Email", "last_login_at": "Last login at", "verified_at": "Verified at", "reset_password": "Reset password", "impersonate": "Impersonate", "role": "Role", "delete": {"title": "Delete <<name>>?", "description": "Are you sure you want to delete this user?"}, "reset-password": {"title": "Reset password?", "description": "Are you sure you want to reset the password for <<name>>?", "password": "Password"}}, "detail": {"general": {"title": "General", "description": "Edit the general information about the user", "firstname": "Firstname", "lastname": "Lastname", "email": "Email", "role": "Role"}}}, "invoices": {"number": "Number", "status": "Status", "total_including_vat": "Total including VAT", "total_excluding_vat": "Total excluding VAT", "date": "Date", "stripe": "Stripe", "statuses": {"draft": "Draft", "open": "Open", "paid": "Paid", "uncollectible": "Un collectible", "void": "Void"}}, "features": {"items": {"ZAPIER": "Zapier", "MAKE": "Make.com", "CUSTOM_CONNECTIONS": "Custom connections"}}}}, "emails": {"index": {"title": "Emails", "table": {"name": "Name", "event": "Event", "whitelabel": "Whitelabel", "updated_at": "Updated at", "updated_by": "Updated by"}}, "detail": {"tabs": {"general": "General", "template": "Template"}, "general": {"name": "Name", "whitelabel": "Whitelabel", "subject": "Subject"}, "template": {"variables": "Variable legend", "table": {"name": "Name", "how_to_use": "How to use"}}}}, "support": {"index": {"title": "Support", "accounts": "Select accounts...", "select_statuses": "Select statuses...", "table": {"number": "Number", "status": "Status", "title": "Title", "created_by": "Created by", "updated_at": "Updated at"}, "create_ticket": "Create ticket", "statuses": {"OPEN": "Open", "IN_PROGRESS": "In progress", "RESOLVED": "Resolved", "CLOSED": "Closed", "REOPENED": "Reopened", "PENDING_CLIENT": "Pending client"}}, "detail": {"title": "Title", "description": "Description", "attachments": "Attachments (<<count>>)", "message": "Message", "status": "Status"}}}, "layouts": {"container": {"home": "Home", "accounts": "Accounts", "support": "Support", "emails": "Emails"}}, "components": {"pagination": {"showing": "Showing", "to": "to", "results": "results", "previous": "Previous", "of": "of", "next": "Next"}}, "enums": {"user-role": {"ADMIN": "Admin", "USER": "User"}, "business-type": {"LEAD_GENERATION": "Lead Generation", "SAAS": "SaaS", "E_COMMERCE": "E-commerce"}}}