# Symlink Module Resolution Fix

## Problem
When using the emailPlugin from a symlinked folder (`front-end/admin/src/packages` -> `../../packages`), the following error occurred:

```
emails.routes.ts:13 ERROR TypeError: Failed to resolve module specifier "juice". Relative references must start with either "/", "./", or "../".
```

This happened because when using symlinks, the module resolution for third-party packages like `juice` fails when the code is executed from the symlinked location.

## Solution
Instead of using a symlink, we now copy the packages directory directly into the admin project. This avoids the module resolution issues entirely.

## How to Use

### Option 1: Use the sync script (Recommended)
```bash
cd front-end/admin
./sync-packages.sh
```

### Option 2: Manual copy
```bash
cd front-end/admin
rm -rf src/packages
cp -r ../packages src/packages
```

## When to Sync
You need to sync the packages whenever:
- You make changes to files in `front-end/packages/`
- You pull changes that include updates to the packages directory
- You're setting up the project for the first time

## Technical Details
- The `juice` package is used by the email plugin for CSS inlining
- The issue was specifically with symlink module resolution in the browser environment
- Copying the files directly resolves the module path correctly
- The TypeScript path mapping `"@packages/*": ["src/packages/*"]` works correctly with copied files

## Files Modified
- Removed symlink: `front-end/admin/src/packages`
- Added sync script: `front-end/admin/sync-packages.sh`
- Copied packages: `front-end/admin/src/packages/` (now contains actual files)

## Verification
After syncing, you should be able to:
1. Run `ng serve` without module resolution errors
2. Use the email editor with the emailPlugin functionality
3. Build the project successfully with `ng build`
