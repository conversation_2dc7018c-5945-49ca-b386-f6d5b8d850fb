{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"portal": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/portal", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "preserveSymlinks": true, "assets": [{"glob": "**/*", "input": "public"}, "src/config.json"], "styles": ["src/styles/tailwind.css", "./node_modules/ng-zorro-antd/date-picker/style/index.min.css", "src/styles/styles.scss", "node_modules/@ngxpert/hot-toast/src/styles/styles.scss", "./node_modules/grapesjs/dist/css/grapes.min.css"], "scripts": ["./node_modules/grapesjs/dist/grapes.min.js"], "allowedCommonJsDependencies": ["<PERSON><PERSON><PERSON>", "juice"], "externalDependencies": ["juice"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "3MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "portal:build:production"}, "development": {"buildTarget": "portal:build:development"}}, "defaultConfiguration": "development", "options": {"proxyConfig": "proxy/proxy.conf.js", "host": "admin.eaglo.dev", "allowedHosts": ["admin.eaglo.dev"], "sslKey": "./proxy/admin.eaglo.dev-key.pem", "sslCert": "./proxy/admin.eaglo.dev.pem", "ssl": true, "port": 4202}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["./node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "src/styles.scss"], "scripts": []}}}}}}