#!/bin/bash

# <PERSON><PERSON>t to sync packages from front-end/packages to front-end/admin/src/packages
# This fixes the symlink module resolution issues with the juice package

echo "Syncing packages from ../packages to src/packages..."

# Remove existing packages directory
rm -rf src/packages

# Copy packages from parent directory
cp -r ../packages src/packages

echo "Packages synced successfully!"
echo "You can now run 'npm start' or 'ng serve' without module resolution errors."
